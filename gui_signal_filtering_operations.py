# -*- coding: utf-8 -*-
"""
GUI信号筛选操作模块
实现基于用户选择的Excel文件中的股票列表，实时监控每只股票的交易信号，
当信号值匹配用户设定的目标值时，自动筛选并保存符合条件的股票。
"""

import threading
import tkinter as tk
from tkinter import messagebox, filedialog
import logging
import pandas as pd
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

from enhanced_signal_analyzer import SignalAnalyzer
from unified_signal_monitor import UnifiedSignalMonitor
from config import APP_CONFIG, SIGNAL_CONFIG


class SignalFilteringMonitor(UnifiedSignalMonitor):
    """信号筛选监控器，继承统一信号监控器，专门用于信号筛选功能"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any], target_signal: str):
        """
        初始化信号筛选监控器
        
        Args:
            compass_automator: 指南针自动化器实例
            signal_analyzer: 信号分析器实例
            config: 监控配置
            target_signal: 目标信号类型 ('开', '清', '持')
        """
        # 根据目标信号确定监控类型
        signal_type = 'buy' if target_signal == '开' else 'sell' if target_signal == '清' else 'buy'
        super().__init__(compass_automator, signal_analyzer, config, signal_type)
        
        self.target_signal = target_signal
        self.matched_stocks = []  # 符合条件的股票
        self.unmatched_stocks = []  # 不符合条件的股票
        
        # 信号筛选不需要热身功能，直接进行信号检测
        self.config['enable_stock_warmup'] = False
        
        self.logger.info(f"信号筛选监控器初始化完成，目标信号: {target_signal}")
    
    def _process_single_stock(self, stock_code: str, message_queue) -> bool:
        """
        处理单个股票的信号识别（信号筛选版本）
        简化流程：移除热身步骤，直接进行信号检测
        
        Args:
            stock_code: 股票代码
            message_queue: 消息队列
            
        Returns:
            是否处理成功
        """
        current_stock_code = str(stock_code)
        
        try:
            self.logger.debug(f"开始筛选股票: {current_stock_code}")
            
            # 搜索股票（包含页面加载检测）
            search_success = self.compass_automator.search_stock(current_stock_code, wait_for_load=True)
            if not search_success:
                message_queue.put(("log", f"❌ 股票 {current_stock_code} 搜索失败，跳过"))
                self._add_to_unmatched(current_stock_code, "搜索失败")
                return False

            # 页面稳定等待
            additional_wait = self.config.get('page_switch_wait', 1.0)
            if additional_wait > 0:
                time.sleep(additional_wait)

            # 重置鼠标位置（确保操作准确性）
            if hasattr(self, 'reset_mouse_position') and callable(self.reset_mouse_position):
                try:
                    if self.reset_mouse_position():
                        self.logger.debug(f"股票 {current_stock_code} 鼠标已重置到基准位置")
                    else:
                        self.logger.warning(f"股票 {current_stock_code} 鼠标位置重置失败或未设置基准位置")
                except Exception as e:
                    self.logger.error(f"股票 {current_stock_code} 重置鼠标位置时出错: {str(e)}")
            else:
                self.logger.debug(f"股票 {current_stock_code} 未设置鼠标重置方法，跳过鼠标重置")

            # 直接进行信号识别（跳过热身）
            try:
                signal = self.signal_analyzer.recognize_signal(current_stock_code)
                if not signal:
                    message_queue.put(("log", f"⚠️ 股票 {current_stock_code} 信号识别失败"))
                    self._add_to_unmatched(current_stock_code, "信号识别失败")
                    return False
                
                # 检查信号是否匹配目标
                if signal == self.target_signal:
                    message_queue.put(("log", f"✅ 股票 {current_stock_code} 信号匹配: {signal}"))
                    self._add_to_matched(current_stock_code, signal)
                else:
                    message_queue.put(("log", f"📊 股票 {current_stock_code} 信号不匹配: {signal} (目标: {self.target_signal})"))
                    self._add_to_unmatched(current_stock_code, f"信号不匹配({signal})")
                
                return True
                
            except Exception as e:
                self.logger.error(f"股票 {current_stock_code} 信号识别异常: {str(e)}")
                message_queue.put(("log", f"❌ 股票 {current_stock_code} 信号识别异常: {str(e)}"))
                self._add_to_unmatched(current_stock_code, f"识别异常: {str(e)}")
                return False
                
        except Exception as e:
            self.logger.error(f"处理股票 {current_stock_code} 时出错: {str(e)}")
            message_queue.put(("log", f"❌ 股票 {current_stock_code} 处理出错: {str(e)}"))
            self._add_to_unmatched(current_stock_code, f"处理出错: {str(e)}")
            return False
    
    def _add_to_matched(self, stock_code: str, signal: str):
        """添加到符合条件的股票列表"""
        stock_info = {
            'stock_code': stock_code,
            'signal': signal,
            'match_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': '符合条件'
        }
        self.matched_stocks.append(stock_info)
    
    def _add_to_unmatched(self, stock_code: str, reason: str):
        """添加到不符合条件的股票列表"""
        stock_info = {
            'stock_code': stock_code,
            'reason': reason,
            'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': '不符合条件'
        }
        self.unmatched_stocks.append(stock_info)
    
    def get_filtering_results(self) -> Dict[str, List[Dict]]:
        """获取筛选结果"""
        return {
            'matched': self.matched_stocks,
            'unmatched': self.unmatched_stocks,
            'target_signal': self.target_signal
        }


class GUISignalFilteringOperationsMixin:
    """GUI信号筛选操作Mixin类"""
    
    def init_signal_filtering_components(self):
        """初始化信号筛选相关组件"""
        try:
            # 初始化信号筛选状态
            self.is_signal_filtering = False
            self.signal_filtering_monitor = None
            self.signal_filtering_analyzer = None
            
            # 信号筛选相关变量
            self.target_signal_var = tk.StringVar(value="持仓")  # 默认选择持仓
            
            self.logger.info("信号筛选组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"信号筛选组件初始化失败: {str(e)}")
    
    def start_signal_filtering(self):
        """启动信号筛选"""
        try:
            # 检查前置条件
            if not self._check_signal_filtering_prerequisites():
                return
            
            # 检查是否已在运行
            if self.is_signal_filtering:
                messagebox.showwarning("警告", "信号筛选已在运行中")
                return
            
            # 获取Excel文件路径
            excel_path = self.excel_path_var.get().strip()
            if not excel_path:
                messagebox.showerror("错误", "请先选择Excel文件")
                return
            
            # 获取目标信号
            target_signal_text = self.target_signal_var.get()
            target_signal = self._convert_signal_text_to_code(target_signal_text)
            
            self.logger.info(f"开始启动信号筛选，目标信号: {target_signal_text}({target_signal})")
            
            # 检查并建立指南针连接
            if not self._ensure_compass_connection():
                return
            
            # 创建分析器和监控器
            if not self._create_signal_filtering_components(target_signal):
                return
            
            # 在新线程中启动筛选
            self._start_signal_filtering_thread(excel_path, target_signal)
            
        except Exception as e:
            self.logger.error(f"启动信号筛选失败: {str(e)}")
            messagebox.showerror("错误", f"启动筛选失败: {str(e)}")
    
    def stop_signal_filtering(self):
        """停止信号筛选"""
        try:
            if not self.is_signal_filtering:
                messagebox.showinfo("提示", "信号筛选未在运行")
                return
            
            self.logger.info("正在停止信号筛选")
            
            # 停止监控
            if self.signal_filtering_monitor:
                self.signal_filtering_monitor.stop_monitoring()
            
            # 更新UI状态
            self._update_signal_filtering_ui_state(False)
            
            self.message_queue.put(("log", "🛑 用户请求停止信号筛选"))
            
        except Exception as e:
            self.logger.error(f"停止信号筛选失败: {str(e)}")
            messagebox.showerror("错误", f"停止筛选失败: {str(e)}")
    
    def _check_signal_filtering_prerequisites(self) -> bool:
        """检查信号筛选的前置条件"""
        # 检查信号区域配置
        if not hasattr(self, 'selected_buy_signal_region') or not self.selected_buy_signal_region:
            messagebox.showerror("错误", "请先选择信号区域")
            return False
        
        # 检查OCR初始化
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化，请重启程序")
            return False
        
        return True
    
    def _convert_signal_text_to_code(self, signal_text: str) -> str:
        """将界面显示的信号文本转换为信号代码"""
        signal_mapping = {
            "开仓": "开",
            "清仓": "清", 
            "持仓": "持"
        }
        return signal_mapping.get(signal_text, "持")
    
    def _create_signal_filtering_components(self, target_signal: str) -> bool:
        """创建信号筛选组件"""
        try:
            from config import COMPASS_SOFTWARE
            
            # 创建信号分析器（传递消息队列以支持GUI日志输出）
            signal_region = COMPASS_SOFTWARE.get('buy_signal_region', {})
            self.signal_filtering_analyzer = SignalAnalyzer(
                self.ocr_manager, signal_region, APP_CONFIG, self.message_queue
            )
            
            # 创建信号筛选监控器
            self.signal_filtering_monitor = SignalFilteringMonitor(
                self.compass_automator,
                self.signal_filtering_analyzer,
                SIGNAL_CONFIG,
                target_signal
            )

            # 设置鼠标重置方法（如果存在）
            if hasattr(self, 'move_mouse_to_target_position'):
                self.signal_filtering_monitor.reset_mouse_position = self.move_mouse_to_target_position
                self.logger.debug("已为信号筛选监控器设置鼠标重置方法")
            
            self.logger.info("信号筛选组件创建成功")
            return True

        except Exception as e:
            self.logger.error(f"创建信号筛选组件失败: {str(e)}")
            messagebox.showerror("错误", f"创建筛选组件失败: {str(e)}")
            return False

    def _start_signal_filtering_thread(self, excel_path: str, target_signal: str):
        """启动信号筛选线程"""
        def filtering_thread():
            try:
                # 读取股票代码
                self.message_queue.put(("log", f"📋 正在读取Excel文件: {excel_path}"))
                stock_codes = self.signal_filtering_monitor.load_stock_codes(excel_path)

                if not stock_codes:
                    self.message_queue.put(("log", "❌ 未找到有效的股票代码"))
                    self.message_queue.put(("signal_filtering_stopped", None))
                    return

                # 更新UI状态
                self.message_queue.put(("signal_filtering_started", len(stock_codes)))

                # 开始筛选
                self.message_queue.put(("log", f"🎯 开始信号筛选，目标信号: {target_signal}，股票数量: {len(stock_codes)}"))

                # 启动监控（这里实际上是一次性筛选，不是持续监控）
                success = self.signal_filtering_monitor.start_monitoring(stock_codes, self.message_queue)

                if success:
                    # 等待筛选完成
                    while self.signal_filtering_monitor.is_monitoring and not self.signal_filtering_monitor.stop_requested:
                        time.sleep(0.5)

                    # 获取筛选结果
                    results = self.signal_filtering_monitor.get_filtering_results()

                    # 保存结果
                    self._save_filtering_results(results, target_signal)

                    # 发送完成消息
                    self.message_queue.put(("signal_filtering_complete", results))
                else:
                    self.message_queue.put(("log", "❌ 信号筛选启动失败"))
                    self.message_queue.put(("signal_filtering_stopped", None))

            except Exception as e:
                self.logger.error(f"信号筛选线程异常: {str(e)}")
                self.message_queue.put(("log", f"❌ 信号筛选异常: {str(e)}"))
                self.message_queue.put(("signal_filtering_stopped", None))

        # 启动线程
        thread = threading.Thread(target=filtering_thread, daemon=True)
        thread.start()

    def _save_filtering_results(self, results: Dict[str, Any], target_signal: str):
        """保存筛选结果到Excel文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            signal_name = self._convert_signal_code_to_text(target_signal)

            # 确保analysis_results目录存在
            results_dir = "analysis_results"
            if not os.path.exists(results_dir):
                os.makedirs(results_dir)

            # 保存符合条件的股票
            matched_stocks = results.get('matched', [])
            if matched_stocks:
                matched_file = os.path.join(results_dir, f"信号筛选结果_符合_{signal_name}_{timestamp}.xlsx")
                self._save_stocks_to_excel(matched_stocks, matched_file, "符合条件")
                self.message_queue.put(("log", f"📁 符合条件的股票已保存: {matched_file}"))

            # 保存不符合条件的股票
            unmatched_stocks = results.get('unmatched', [])
            if unmatched_stocks:
                unmatched_file = os.path.join(results_dir, f"信号筛选结果_不符合_{signal_name}_{timestamp}.xlsx")
                self._save_stocks_to_excel(unmatched_stocks, unmatched_file, "不符合条件")
                self.message_queue.put(("log", f"📁 不符合条件的股票已保存: {unmatched_file}"))

            # 统计信息
            matched_count = len(matched_stocks)
            unmatched_count = len(unmatched_stocks)
            total_count = matched_count + unmatched_count

            self.message_queue.put(("log", f"📊 筛选完成统计: 总计{total_count}只股票，符合条件{matched_count}只，不符合条件{unmatched_count}只"))

        except Exception as e:
            self.logger.error(f"保存筛选结果失败: {str(e)}")
            self.message_queue.put(("log", f"❌ 保存筛选结果失败: {str(e)}"))

    def _save_stocks_to_excel(self, stocks: List[Dict], file_path: str, result_type: str):
        """将股票列表保存到Excel文件"""
        try:
            df_data = []

            for stock in stocks:
                if result_type == "符合条件":
                    df_data.append({
                        '股票代码': stock['stock_code'],
                        '匹配信号': stock['signal'],
                        '匹配时间': stock['match_time'],
                        '状态': stock['status']
                    })
                else:  # 不符合条件
                    df_data.append({
                        '股票代码': stock['stock_code'],
                        '不匹配原因': stock['reason'],
                        '检查时间': stock['check_time'],
                        '状态': stock['status']
                    })

            df = pd.DataFrame(df_data)
            df.to_excel(file_path, index=False, engine='openpyxl')

        except Exception as e:
            self.logger.error(f"保存Excel文件失败: {str(e)}")
            raise

    def _convert_signal_code_to_text(self, signal_code: str) -> str:
        """将信号代码转换为界面显示文本"""
        code_mapping = {
            "开": "开仓",
            "清": "清仓",
            "持": "持仓"
        }
        return code_mapping.get(signal_code, "持仓")

    def _update_signal_filtering_ui_state(self, is_running: bool):
        """更新信号筛选UI状态"""
        try:
            self.is_signal_filtering = is_running

            if hasattr(self, 'signal_filtering_start_btn'):
                self.signal_filtering_start_btn.config(state=tk.DISABLED if is_running else tk.NORMAL)

            if hasattr(self, 'signal_filtering_stop_btn'):
                self.signal_filtering_stop_btn.config(state=tk.NORMAL if is_running else tk.DISABLED)

        except Exception as e:
            self.logger.error(f"更新信号筛选UI状态失败: {str(e)}")

    def handle_signal_filtering_messages(self, message_type: str, data):
        """处理信号筛选相关的消息"""
        try:
            if message_type == "signal_filtering_started":
                stock_count = data
                self._update_signal_filtering_ui_state(True)
                self.logger.info(f"信号筛选已启动，股票数量: {stock_count}")

            elif message_type == "signal_filtering_stopped":
                self._update_signal_filtering_ui_state(False)
                self.logger.info("信号筛选已停止")

            elif message_type == "signal_filtering_complete":
                results = data
                self._update_signal_filtering_ui_state(False)

                # 显示完成消息
                matched_count = len(results.get('matched', []))
                unmatched_count = len(results.get('unmatched', []))
                total_count = matched_count + unmatched_count

                messagebox.showinfo("筛选完成",
                    f"信号筛选已完成！\n\n"
                    f"总计处理: {total_count} 只股票\n"
                    f"符合条件: {matched_count} 只\n"
                    f"不符合条件: {unmatched_count} 只\n\n"
                    f"结果已保存到 analysis_results 目录")

                self.logger.info(f"信号筛选完成: 总计{total_count}只，符合{matched_count}只，不符合{unmatched_count}只")

        except Exception as e:
            self.logger.error(f"处理信号筛选消息失败: {str(e)}")

    def get_signal_filtering_status(self) -> Dict[str, Any]:
        """获取信号筛选状态信息"""
        if self.signal_filtering_monitor:
            return self.signal_filtering_monitor.get_monitoring_stats()
        return {}
