# -*- coding: utf-8 -*-
"""
钉钉机器人通知模块
支持加签验证的钉钉机器人消息发送
"""

import hmac
import hashlib
import base64
import time
import urllib.parse
import json
import requests
import logging
from typing import Optional, Dict, Any, List
from threading import Lock
import os
import datetime
import openpyxl


class DingTalkNotifier:
    """钉钉机器人通知器"""
    
    def __init__(self, webhook_url: str, secret: str):
        """
        初始化钉钉通知器
        
        Args:
            webhook_url: 钉钉机器人webhook地址
            secret: 加签密钥
        """
        self.logger = logging.getLogger(__name__)
        self.webhook_url = webhook_url
        self.secret = secret
        
        # 初始化Excel写入锁
        self._log_lock = Lock()
        
        # Excel写入性能优化的缓存变量
        self._current_date = None
        self._current_filepath = None
        self._header_written = False
        
        self.logger.info("钉钉机器人通知器初始化完成")
    
    def _generate_sign(self, timestamp: str) -> str:
        """
        生成钉钉机器人加签
        
        Args:
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        string_to_sign = f"{timestamp}\n{self.secret}"
        string_to_sign_enc = string_to_sign.encode('utf-8')
        secret_enc = self.secret.encode('utf-8')
        
        hmac_code = hmac.new(
            secret_enc, 
            string_to_sign_enc, 
            digestmod=hashlib.sha256
        ).digest()
        
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign
    
    def _build_request_url(self) -> str:
        """
        构建带签名的请求URL
        
        Returns:
            完整的请求URL
        """
        timestamp = str(round(time.time() * 1000))
        sign = self._generate_sign(timestamp)
        
        # 在原有webhook URL基础上添加签名参数
        separator = '&' if '?' in self.webhook_url else '?'
        return f"{self.webhook_url}{separator}timestamp={timestamp}&sign={sign}"
    
    def send_text_message(self, message: str, at_all: bool = False) -> bool:
        """
        发送文本消息
        
        Args:
            message: 消息内容
            at_all: 是否@所有人
            
        Returns:
            是否发送成功
        """
        try:
            # 构建消息体
            data = {
                "msgtype": "text",
                "text": {
                    "content": message
                },
                "at": {
                    "atMobiles": [],
                    "isAtAll": at_all
                }
            }
            
            # 构建请求URL
            url = self._build_request_url()
            
            # 发送请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(
                url, 
                data=json.dumps(data, ensure_ascii=False).encode('utf-8'),
                headers=headers,
                timeout=10
            )
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info(f"钉钉消息发送成功: {message[:50]}...")
                    return True
                else:
                    self.logger.error(f"钉钉消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                self.logger.error(f"钉钉消息发送失败，HTTP状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送钉钉消息异常: {str(e)}")
            return False
    
    def send_markdown_message(self, title: str, markdown_text: str, at_all: bool = False) -> bool:
        """
        发送Markdown格式消息
        
        Args:
            title: 消息标题
            markdown_text: Markdown格式的消息内容
            at_all: 是否@所有人
            
        Returns:
            是否发送成功
        """
        try:
            # 构建消息体
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": markdown_text
                },
                "at": {
                    "atMobiles": [],
                    "isAtAll": at_all
                }
            }
            
            # 构建请求URL
            url = self._build_request_url()
            
            # 发送请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(
                url, 
                data=json.dumps(data, ensure_ascii=False).encode('utf-8'),
                headers=headers,
                timeout=10
            )
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info(f"钉钉Markdown消息发送成功: {title}")
                    return True
                else:
                    self.logger.error(f"钉钉Markdown消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                self.logger.error(f"钉钉Markdown消息发送失败，HTTP状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送钉钉Markdown消息异常: {str(e)}")
            return False
    
    def send_stock_signal_notification(self, stock_code: str, signal: str, 
                                     change_type: str = "首次检测", 
                                     old_signal: Optional[str] = None) -> bool:
        """
        发送股票信号通知
        
        Args:
            stock_code: 股票代码
            signal: 当前信号
            change_type: 变化类型（首次检测、信号变化）
            old_signal: 旧信号（仅在信号变化时提供）
            
        Returns:
            是否发送成功
        """
        try:
            current_time = time.strftime('%H:%M:%S', time.localtime())
            
            # 特殊处理清仓信号 - 无论在什么监控中都要醒目提醒
            if signal == "清仓":
                if change_type == "首次检测":
                    message = f"🚨⛔🔥💀 清仓信号警报！！💀🔥⛔🚨\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"⚠️ 信号状态: {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"💥 类型: 首次检测到清仓信号\n" \
                             f"🔴 危险等级: 高危！建议立即关注！\n\n" \
                             f"🚨 警告：请立即检查该股票！ 🚨@所有人"
                else:
                    message = f"🚨⛔🔥💀 清仓信号警报！！💀🔥⛔🚨\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"🔄 信号变化: {old_signal} → {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"💥 类型: 信号变化为清仓\n" \
                             f"🔴 危险等级: 高危！建议立即关注！\n\n" \
                             f"🚨 警告：请及时关注信号变化！ 🚨@所有人"
                success = self.send_text_message(message, at_all=True)  # 清仓信号@所有人
                if success:
                    self._record_notification(stock_code, signal, change_type, old_signal, True, message)
                return success
            
            elif change_type == "首次检测" and signal == "开仓":
                # 首次检测到开仓信号
                message = f"🚀 股票信号提醒\n\n" \
                         f"📈 股票代码: {stock_code}\n" \
                         f"🎯 信号状态: {signal}\n" \
                         f"⏰ 时间: {current_time}\n" \
                         f"🔔 类型: 首次检测到开仓信号"
                
            elif change_type == "信号变化":
                # 信号发生变化
                if signal == "开仓":
                    # 变化为开仓信号
                    message = f"🚀 股票信号变化提醒\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"🔄 信号变化: {old_signal} → {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"🎯 重要: 出现开仓信号！"
                else:
                    # 其他信号变化
                    message = f"📊 股票信号变化提醒\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"🔄 信号变化: {old_signal} → {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"📝 类型: 信号状态变化"
            else:
                # 其他情况
                message = f"📊 股票信号提醒\n\n" \
                         f"📈 股票代码: {stock_code}\n" \
                         f"🎯 信号状态: {signal}\n" \
                         f"⏰ 时间: {current_time}\n" \
                         f"📝 类型: {change_type}"
            
            success = self.send_text_message(message, at_all=False)
            if success:
                self._record_notification(stock_code, signal, change_type, old_signal, False, message)
            return success
            
        except Exception as e:
            self.logger.error(f"发送股票信号通知异常: {str(e)}")
            return False
    
    def send_sell_signal_notification(self, stock_code: str, signal: str, 
                                     change_type: str = "首次检测", 
                                     old_signal: Optional[str] = None,
                                     is_danger: bool = False) -> bool:
        """
        发送卖出信号通知（专为卖出信号设计的危险提醒）
        
        Args:
            stock_code: 股票代码
            signal: 当前信号
            change_type: 变化类型（首次检测、信号变化、持仓转清仓）
            old_signal: 旧信号（仅在信号变化时提供）
            is_danger: 是否是危险信号（清仓）
            
        Returns:
            是否发送成功
        """
        try:
            current_time = time.strftime('%H:%M:%S', time.localtime())
            
            if is_danger and signal == "清仓":
                if change_type == "首次检测":
                    # 首次检测到清仓信号
                    message = f"🚨⛔🔥💀 清仓信号警报！！💀🔥⛔🚨\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"⚠️ 信号状态: {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"💥 类型: 首次检测到清仓信号\n" \
                             f"🔴 危险等级: 高危！建议立即关注！\n\n" \
                             f"🚨 警告：请立即检查该股票！ 🚨@所有人"
                
                elif change_type == "持仓转清仓":
                    # 从持仓变为清仓的特殊情况
                    message = f"🚨⛔🔥💀 清仓信号警报！！💀🔥⛔🚨\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"🔄 信号变化: {old_signal} → {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"💥 类型: 持仓股票转为清仓信号\n" \
                             f"🔴 危险等级: 极高危！持仓股票发出卖出信号！！\n\n" \
                             f"🚨 警告：持仓转清仓！立即行动！ 🚨@所有人"
                
                else:
                    # 其他清仓信号变化
                    message = f"🚨⛔🔥💀 清仓信号警报！！💀🔥⛔🚨\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"🔄 信号变化: {old_signal} → {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"💥 类型: 信号变化为清仓\n" \
                             f"🔴 危险等级: 高危！建议立即关注！\n\n" \
                             f"🚨 警告：请及时关注清仓信号！ 🚨@所有人"
            
            else:
                # 非危险信号的正常通知
                if change_type == "首次检测":
                    message = f"📊 卖出信号监控提醒\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"🎯 信号状态: {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"🔔 类型: 首次检测到信号"
                
                else:
                    message = f"📊 卖出信号变化提醒\n\n" \
                             f"📈 股票代码: {stock_code}\n" \
                             f"🔄 信号变化: {old_signal} → {signal}\n" \
                             f"⏰ 时间: {current_time}\n" \
                             f"📝 类型: 信号状态变化"
            
            # 使用危险信号时@所有人
            success = self.send_text_message(message, at_all=is_danger)
            if success:
                self._record_notification(stock_code, signal, change_type, old_signal, is_danger, message)
            return success
            
        except Exception as e:
            self.logger.error(f"发送卖出信号通知异常: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试钉钉机器人连接
        
        Returns:
            是否连接成功
        """
        try:
            test_message = "🤖 钉钉机器人连接测试\n\n" \
                          f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}\n" \
                          f"✅ 股票监控系统钉钉通知功能正常"
            
            return self.send_text_message(test_message)
            
        except Exception as e:
            self.logger.error(f"测试钉钉机器人连接异常: {str(e)}")
            return False
    
    def _write_single_record_to_excel(self, row_data: list, stock_code: str):
        """
        单条记录同步写入Excel文件（高效优化版本）
        
        Args:
            row_data: 要写入的行数据
            stock_code: 股票代码（用于日志）
        """
        if not row_data:
            return
        
        try:
            # 获取当日日期和文件路径（优化：缓存日期检查）
            current_date = datetime.date.today()
            if self._current_date != current_date:
                # 日期发生变化，更新缓存
                self._current_date = current_date
                logs_dir = "logs"
                if not os.path.exists(logs_dir):
                    os.makedirs(logs_dir)
                
                date_str = current_date.strftime("%Y%m%d")
                self._current_filepath = os.path.join(logs_dir, f"dingtalk_notifications_{date_str}.xlsx")
                self._header_written = os.path.exists(self._current_filepath)
                
                self.logger.debug(f"日期更新，新的Excel文件路径: {self._current_filepath}")
            
            with self._log_lock:
                # 高效文件操作：避免重复加载
                if self._header_written:
                    try:
                        # 文件已存在，使用追加模式
                        wb = openpyxl.load_workbook(self._current_filepath)
                        ws = wb.active
                    except Exception as e:
                        self.logger.warning(f"加载现有Excel文件失败，重新创建: {str(e)}")
                        wb = openpyxl.Workbook()
                        ws = wb.active
                        self._write_excel_header(ws)
                        self._header_written = True
                else:
                    # 创建新文件
                    wb = openpyxl.Workbook()
                    ws = wb.active
                    self._write_excel_header(ws)
                    self._header_written = True
                
                # 追加数据行（单条记录高效追加）
                ws.append(row_data)
                
                # 保存并立即关闭文件（释放资源）
                wb.save(self._current_filepath)
                wb.close()  # 显式关闭释放资源
                
                self.logger.debug(f"成功写入股票 {stock_code} 的通知记录")
                
        except PermissionError as e:
            self.logger.error(f"Excel文件被占用，无法写入: {str(e)}")
            # 文件被占用时的备用策略：生成带时间戳的文件名
            try:
                timestamp = datetime.datetime.now().strftime("%H%M%S")
                backup_filepath = self._current_filepath.replace('.xlsx', f'_backup_{timestamp}.xlsx')
                wb = openpyxl.Workbook()
                ws = wb.active
                self._write_excel_header(ws)
                ws.append(row_data)
                wb.save(backup_filepath)
                wb.close()
                self.logger.info(f"使用备用文件成功写入: {backup_filepath}")
            except Exception as backup_e:
                self.logger.error(f"备用写入也失败: {str(backup_e)}")
                
        except Exception as e:
            self.logger.error(f"写入钉钉通知Excel文件失败: {str(e)}")
            # 在关键错误时记录详细信息
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _write_excel_header(self, worksheet):
        """写入Excel表头"""
        headers = [
            "日期", "时间", "股票代码", "当前信号", "旧信号", 
            "变更类型", "@所有人", "完整消息", "消息长度"
        ]
        worksheet.append(headers)
    
    def _record_notification(self, stock_code: str, signal: str, change_type: str, 
                           old_signal: Optional[str], at_all: bool, message: str):
        """
        记录钉钉通知到Excel文件（同步版本）
        
        Args:
            stock_code: 股票代码
            signal: 当前信号
            change_type: 变更类型
            old_signal: 旧信号
            at_all: 是否@所有人
            message: 完整消息内容
        """
        try:
            # 确保股票代码的独立性（避免引用问题）
            stock_code_copy = str(stock_code)
            signal_copy = str(signal)
            change_type_copy = str(change_type)
            old_signal_copy = str(old_signal) if old_signal else ""
            
            now = datetime.datetime.now()
            row_data = [
                now.strftime("%Y-%m-%d"),  # 日期
                now.strftime("%H:%M:%S"),  # 时间
                stock_code_copy,           # 股票代码
                signal_copy,               # 当前信号
                old_signal_copy,           # 旧信号
                change_type_copy,          # 变更类型
                "是" if at_all else "否",   # @所有人
                str(message),              # 完整消息
                len(str(message))          # 消息长度
            ]
            
            # 直接同步写入Excel
            self._write_single_record_to_excel(row_data, stock_code_copy)
            
        except Exception as e:
            self.logger.error(f"记录钉钉通知失败: {str(e)}")
    


def create_dingtalk_notifier_from_config() -> Optional[DingTalkNotifier]:
    """
    从配置创建钉钉通知器
    
    Returns:
        钉钉通知器实例，如果配置无效则返回None
    """
    try:
        # 从配置文件读取钉钉配置
        from config import DINGTALK_CONFIG
        
        if not DINGTALK_CONFIG.get('enabled', False):
            logging.getLogger(__name__).info("钉钉通知功能已被禁用")
            return None
        
        webhook_url = DINGTALK_CONFIG.get('webhook_url')
        secret = DINGTALK_CONFIG.get('secret')
        
        if not webhook_url or not secret:
            logging.getLogger(__name__).warning("钉钉配置不完整，跳过通知功能")
            return None
        
        return DingTalkNotifier(webhook_url, secret)
        
    except ImportError:
        logging.getLogger(__name__).error("无法导入钉钉配置，请检查config.py文件")
        return None
    except Exception as e:
        logging.getLogger(__name__).error(f"创建钉钉通知器失败: {str(e)}")
        return None


def create_dingtalk_notifier_by_group(group_name: str = None) -> Optional[DingTalkNotifier]:
    """
    根据钉钉群名称创建通知器
    
    Args:
        group_name: 钉钉群名称，如果为None则使用当前选中的群
        
    Returns:
        钉钉通知器实例，如果配置无效则返回None
    """
    try:
        # 从配置文件读取钉钉群配置
        from config import DINGTALK_CONFIG, DINGTALK_GROUPS
        
        if not DINGTALK_CONFIG.get('enabled', False):
            logging.getLogger(__name__).info("钉钉通知功能已被禁用")
            return None
        
        # 如果没有指定群名称，使用当前选中的群
        if group_name is None:
            group_name = DINGTALK_CONFIG.get('selected_group', '💬 奔向富足')
        
        # 检查群名称是否有效
        if group_name not in DINGTALK_GROUPS:
            logging.getLogger(__name__).error(f"无效的钉钉群名称: {group_name}")
            return None
        
        # 获取群配置
        group_config = DINGTALK_GROUPS[group_name]
        webhook_url = group_config.get('webhook_url')
        secret = group_config.get('secret')
        
        if not webhook_url or not secret:
            logging.getLogger(__name__).warning(f"钉钉群 {group_name} 配置不完整，跳过通知功能")
            return None
        
        logging.getLogger(__name__).info(f"创建钉钉通知器成功，目标群: {group_name}")
        return DingTalkNotifier(webhook_url, secret)
        
    except ImportError:
        logging.getLogger(__name__).error("无法导入钉钉配置，请检查config.py文件")
        return None
    except Exception as e:
        logging.getLogger(__name__).error(f"创建钉钉通知器失败: {str(e)}")
        return None


def get_available_dingtalk_groups() -> List[str]:
    """
    获取可用的钉钉群列表
    
    Returns:
        钉钉群名称列表
    """
    try:
        from config import DINGTALK_GROUPS
        return list(DINGTALK_GROUPS.keys())
    except ImportError:
        logging.getLogger(__name__).error("无法导入钉钉配置")
        return []
    except Exception as e:
        logging.getLogger(__name__).error(f"获取钉钉群列表失败: {str(e)}")
        return []