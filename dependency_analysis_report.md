# 股票筛选器项目依赖分析报告

根据对项目所有Python文件的全面扫描分析，以下是股票筛选器项目的第三方依赖包详细分析结果：

## 实际使用的第三方依赖包

基于代码分析和requirements.txt对比，以下是项目实际使用的第三方依赖包：

### 1. OCR和图像处理 (OCR & Image Processing)

#### 核心OCR引擎
- **paddleocr** (>=3.0)
  - 主要OCR引擎，用于文字识别
  - 使用文件: `simple_paddleocr_engine.py`
  - 功能: 从指南针软件截图中提取股票数据

- **paddlepaddle** (>=2.5.0)
  - PaddleOCR的底层深度学习框架
  - 间接依赖，由paddleocr引用

#### 图像处理库
- **opencv-python** (>=4.8.0)
  - 别名: cv2
  - 使用文件数: 17个
  - 主要文件: `image_processor.py`, `signal_analyzer.py`, `enhanced_signal_analyzer.py`等
  - 功能: 图像预处理、颜色识别、截图处理

- **Pillow** (>=10.0.0)
  - 别名: PIL
  - 使用文件数: 7个
  - 主要文件: `region_selector.py`, `gui_screenshot_operations.py`等
  - 功能: 图像格式转换、基础图像操作

- **numpy** (>=1.24.0)
  - 使用文件数: 18个（项目中最广泛使用的第三方库）
  - 功能: 数组处理、数值计算、图像数据处理

- **mss** (>=9.0.1)
  - 使用文件数: 5个
  - 主要文件: `image_processor.py`, `region_selector.py`
  - 功能: 高性能屏幕截图

#### 可选OCR增强库
- **torch** (>=2.0.0)
  - PyTorch深度学习框架
  - 在requirements.txt中定义，用于EasyOCR支持
  - 项目当前主要使用简化的PaddleOCR模式

- **torchvision** (>=0.15.0)
  - PyTorch的计算机视觉扩展
  - EasyOCR的依赖项

### 2. Windows自动化 (Windows Automation)

- **pywinauto** (>=0.6.8)
  - 使用文件数: 3个
  - 主要文件: `compass_automator.py`, `compass_data_extractor.py`
  - 功能: 与指南针桌面软件交互、窗口控制

- **psutil** (>=5.9.0)
  - 使用文件: `compass_automator.py`
  - 功能: 系统进程管理、软件状态检测

- **pyautogui**
  - 使用文件数: 3个
  - 功能: 鼠标键盘自动化操作
  - **注意**: 该包在requirements.txt中缺失，需要添加

- **keyboard** (>=0.13.5)
  - 使用文件数: 2个
  - 主要文件: `gui_main.py`, `gui_stock_import_operations.py`
  - 功能: 全局键盘事件监听

### 3. Web自动化 (Web Automation)

- **playwright** (>=1.40.0)
  - 使用模块: playwright.async_api
  - 使用文件数: 5个
  - 主要文件: `web_automator.py`, `web_element_selector.py`
  - 功能: 网页自动化、数据抓取

- **beautifulsoup4** (>=4.12.0)
  - 别名: bs4
  - 使用文件: `html_data_parser.py`
  - 功能: HTML解析和数据提取

- **lxml** (>=4.9.0)
  - BeautifulSoup的XML解析后端
  - 在requirements.txt中定义，间接使用

### 4. 数据处理 (Data Processing)

- **pandas** (>=2.0.0)
  - 使用文件数: 6个
  - 主要文件: `data_processor.py`, `web_automator.py`
  - 功能: Excel数据处理、股票筛选逻辑

- **openpyxl** (>=3.1.0)
  - 使用文件: `dingtalk_notifier.py`
  - 功能: Excel文件读写操作

- **xlrd** (>=2.0.0)
  - 在requirements.txt中定义
  - 功能: 旧版Excel文件读取支持

### 5. 网络和HTTP (Network & HTTP)

- **requests** (>=2.31.0)
  - 使用文件: `dingtalk_notifier.py`
  - 功能: 钉钉机器人消息发送、HTTP请求

## 依赖包缺失问题分析

### 1. requirements.txt中缺失的包

根据代码分析，以下包在实际使用但未在requirements.txt中声明：

- **pyautogui** - Windows自动化必需包
  - 建议添加: `pyautogui>=0.9.54`

### 2. 可能的误识别项

分析过程中发现以下项目可能是误识别的本地模块：
- `debug_virtual_scroll`
- `ocr_diagnostics`
- `ocr_performance_wrapper`
- `unified_ocr_component`

这些应该是项目内部模块而非第三方依赖。

### 3. 标准库使用统计

项目大量使用Python标准库，主要包括：
- **tkinter** (28个文件) - GUI界面框架
- **os/logging/typing** (40+个文件) - 核心系统功能
- **threading/asyncio** - 并发处理
- **json/datetime** - 数据格式和时间处理

## 建议的requirements.txt优化

```
# 股票筛选器依赖包 - 优化版本

# Windows自动化
pywinauto>=0.6.8
pyautogui>=0.9.54  # 新增：鼠标键盘自动化
psutil>=5.9.0
keyboard>=0.13.5

# OCR和图像处理
paddleocr>=3.0
paddlepaddle>=2.5.0
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0
mss>=9.0.1

# 数据处理
pandas>=2.0.0
openpyxl>=3.1.0
xlrd>=2.0.0

# Web自动化
playwright>=1.40.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# 网络通信
requests>=2.31.0

# 可选：EasyOCR支持（高级OCR功能）
torch>=2.0.0
torchvision>=0.15.0
```

## 项目架构依赖关系总结

1. **GUI层**: 基于tkinter标准库构建，无额外依赖
2. **自动化层**: 主要依赖pywinauto、pyautogui进行Windows应用控制
3. **OCR层**: 核心使用PaddleOCR，opencv-python提供图像处理支持
4. **数据层**: pandas处理Excel数据，openpyxl提供文件操作
5. **网络层**: requests用于钉钉通知，playwright用于Web数据抓取

总计实际第三方依赖包约15个核心包，项目依赖结构相对精简且功能明确。