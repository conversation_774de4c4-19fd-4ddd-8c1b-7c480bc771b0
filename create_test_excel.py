# -*- coding: utf-8 -*-
"""
创建测试用的Excel文件
用于测试信号筛选功能
"""

import pandas as pd
import os

def create_test_excel():
    """创建测试用的Excel文件"""
    
    # 测试股票代码列表
    test_stocks = [
        "000001",  # 平安银行
        "000002",  # 万科A
        "000858",  # 五粮液
        "002415",  # 海康威视
        "600036",  # 招商银行
        "600519",  # 贵州茅台
        "600887",  # 伊利股份
        "000858",  # 五粮液（重复测试）
    ]
    
    # 创建DataFrame
    df = pd.DataFrame({
        '股票代码': test_stocks
    })
    
    # 保存到Excel文件
    test_file = "test_signal_filtering_stocks.xlsx"
    df.to_excel(test_file, index=False, engine='openpyxl')
    
    print(f"✅ 测试Excel文件已创建: {test_file}")
    print(f"包含 {len(test_stocks)} 只股票代码")
    print("股票列表:")
    for i, stock in enumerate(test_stocks, 1):
        print(f"  {i}. {stock}")
    
    return test_file

if __name__ == "__main__":
    create_test_excel()
