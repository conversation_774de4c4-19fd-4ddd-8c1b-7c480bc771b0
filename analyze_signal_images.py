#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票信号图片颜色分析工具
分析清仓、持仓、开仓信号的颜色特征
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime

def is_background_color(rgb):
    """判断是否为背景色（黑色或黄色）"""
    r, g, b = rgb

    # 黑色背景：RGB值都很低
    if r <= 20 and g <= 20 and b <= 20:
        return True

    # 黄色背景：高R、高G、低B
    if r > 200 and g > 180 and b < 50:
        return True

    return False

def analyze_font_colors(image_path, signal_type):
    """专门分析字体颜色特征，排除背景色"""
    print(f"\n=== 分析 {signal_type} 信号字体颜色: {image_path} ===")

    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：文件不存在 {image_path}")
        return None

    # 读取图片
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误：无法读取图片 {image_path}")
        return None

    # 转换为RGB
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    # 获取图片尺寸
    height, width = img.shape[:2]
    print(f"图片尺寸: {width}x{height}")

    # 获取所有像素
    pixels = img_rgb.reshape(-1, 3)

    # 过滤掉背景色，只保留字体颜色
    font_pixels = []
    background_pixels = []

    for pixel in pixels:
        if is_background_color(pixel):
            background_pixels.append(pixel)
        else:
            font_pixels.append(pixel)

    font_pixels = np.array(font_pixels)
    background_pixels = np.array(background_pixels)

    print(f"总像素数: {len(pixels)}")
    print(f"背景像素数: {len(background_pixels)} ({len(background_pixels)/len(pixels)*100:.1f}%)")
    print(f"字体像素数: {len(font_pixels)} ({len(font_pixels)/len(pixels)*100:.1f}%)")

    if len(font_pixels) == 0:
        print("警告：未检测到字体像素！")
        return None

    # 分析字体颜色分布
    unique_colors, counts = np.unique(font_pixels, axis=0, return_counts=True)

    # 按出现频率排序
    sorted_indices = np.argsort(counts)[::-1]
    top_colors = unique_colors[sorted_indices[:5]]  # 前5种字体颜色
    top_counts = counts[sorted_indices[:5]]

    print("\n主要字体颜色 (RGB值 - 出现次数 - 百分比):")
    font_color_info = []
    total_font_pixels = len(font_pixels)

    for i, (color, count) in enumerate(zip(top_colors, top_counts)):
        percentage = (count / total_font_pixels) * 100
        print(f"  {i+1}. RGB({color[0]}, {color[1]}, {color[2]}) - {count}次 - {percentage:.2f}%")

        font_color_info.append({
            'rank': i+1,
            'rgb': [int(color[0]), int(color[1]), int(color[2])],
            'count': int(count),
            'percentage': round(percentage, 2),
            'hex': f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
        })

    # 计算字体颜色的平均值
    avg_font_color = np.mean(font_pixels, axis=0).astype(int)
    print(f"\n字体平均颜色: RGB({avg_font_color[0]}, {avg_font_color[1]}, {avg_font_color[2]})")

    # 分析字体颜色范围
    min_font_rgb = np.min(font_pixels, axis=0)
    max_font_rgb = np.max(font_pixels, axis=0)
    print(f"字体颜色范围: R({min_font_rgb[0]}-{max_font_rgb[0]}) G({min_font_rgb[1]}-{max_font_rgb[1]}) B({min_font_rgb[2]}-{max_font_rgb[2]})")

    # 计算字体颜色的HSV值
    # 将字体像素转换为图像格式以便转换HSV
    if len(font_pixels) > 0:
        # 创建一个临时图像来计算HSV
        temp_img = font_pixels.reshape(1, len(font_pixels), 3).astype(np.uint8)
        temp_img_bgr = cv2.cvtColor(temp_img, cv2.COLOR_RGB2BGR)
        temp_hsv = cv2.cvtColor(temp_img_bgr, cv2.COLOR_BGR2HSV)
        hsv_pixels = temp_hsv.reshape(-1, 3)
        avg_font_hsv = np.mean(hsv_pixels, axis=0).astype(int)
        print(f"字体平均HSV: H({avg_font_hsv[0]}) S({avg_font_hsv[1]}) V({avg_font_hsv[2]})")
    else:
        avg_font_hsv = [0, 0, 0]

    return {
        'signal_type': signal_type,
        'image_path': image_path,
        'dimensions': {'width': width, 'height': height},
        'total_pixels': len(pixels),
        'background_pixels': len(background_pixels),
        'font_pixels': len(font_pixels),
        'font_pixel_ratio': len(font_pixels) / len(pixels),
        'top_font_colors': font_color_info,
        'average_font_rgb': [int(avg_font_color[0]), int(avg_font_color[1]), int(avg_font_color[2])],
        'average_font_hsv': [int(avg_font_hsv[0]), int(avg_font_hsv[1]), int(avg_font_hsv[2])],
        'font_rgb_range': {
            'min': [int(min_font_rgb[0]), int(min_font_rgb[1]), int(min_font_rgb[2])],
            'max': [int(max_font_rgb[0]), int(max_font_rgb[1]), int(max_font_rgb[2])]
        }
    }

def compare_colors(color1, color2):
    """计算两个RGB颜色的欧几里得距离"""
    return np.sqrt(sum((c1 - c2) ** 2 for c1, c2 in zip(color1, color2)))

def analyze_font_color_similarity(results):
    """分析字体颜色相似性"""
    print("\n=== 字体颜色相似性分析 ===")

    signal_types = list(results.keys())

    for i in range(len(signal_types)):
        for j in range(i+1, len(signal_types)):
            type1, type2 = signal_types[i], signal_types[j]
            color1 = results[type1]['average_font_rgb']
            color2 = results[type2]['average_font_rgb']

            distance = compare_colors(color1, color2)
            print(f"{type1} vs {type2}: 字体颜色距离 = {distance:.2f}")

            if distance < 30:
                print(f"  ⚠️  警告：{type1}和{type2}字体颜色极其相似！")
            elif distance < 60:
                print(f"  ⚠️  注意：{type1}和{type2}字体颜色较为相似")
            else:
                print(f"  ✅  {type1}和{type2}字体颜色差异明显")

def main():
    """主函数"""
    print("股票信号颜色分析工具")
    print("=" * 50)
    
    # 图片路径
    image_paths = {
        '清仓信号': 'screenshots/qc2.png',
        '持仓信号': 'screenshots/cc2.png', 
        '开仓信号': 'screenshots/kc2.png'
    }
    
    results = {}
    
    # 分析每张图片
    for signal_type, image_path in image_paths.items():
        result = analyze_font_colors(image_path, signal_type)
        if result:
            results[signal_type] = result
    
    # 字体颜色相似性分析
    if len(results) >= 2:
        analyze_font_color_similarity(results)
    
    # 保存分析结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"signal_color_analysis_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析结果已保存到: {output_file}")
    
    # 生成字体颜色识别建议
    print("\n=== 字体颜色识别建议 ===")

    if '清仓信号' in results:
        qc_color = results['清仓信号']['average_font_rgb']
        print(f"清仓信号字体 (平均RGB): {qc_color}")

    if '持仓信号' in results and '开仓信号' in results:
        cc_color = results['持仓信号']['average_font_rgb']
        kc_color = results['开仓信号']['average_font_rgb']

        print(f"持仓信号字体 (平均RGB): {cc_color}")
        print(f"开仓信号字体 (平均RGB): {kc_color}")

        distance = compare_colors(cc_color, kc_color)
        print(f"持仓与开仓字体颜色距离: {distance:.2f}")

        if distance < 30:
            print("\n⚠️  持仓和开仓信号字体颜色极其相似，建议采用以下策略：")
            print("1. 结合OCR文字识别（'持仓' vs '开仓'）")
            print("2. 分析HSV色彩空间的细微差异")
            print("3. 使用更精细的颜色阈值")
            print("4. 考虑字体渲染的抗锯齿效果")
            print("5. 采用混合识别策略：颜色预筛选 + OCR确认")
        elif distance < 60:
            print("\n⚠️  持仓和开仓信号字体颜色较为相似，建议：")
            print("1. 优化颜色阈值设置")
            print("2. 增加置信度验证")
            print("3. 必要时启用OCR辅助识别")
        else:
            print("\n✅  持仓和开仓信号字体颜色差异明显，可以使用纯颜色识别")

if __name__ == "__main__":
    main()
