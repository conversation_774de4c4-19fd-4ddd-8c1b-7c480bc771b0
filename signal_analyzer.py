# -*- coding: utf-8 -*-
"""
信号分析模块
负责OCR识别和解析信号区域的内容
支持四种信号：持仓、开仓、空仓、清仓
"""

import logging
import re
import time
from typing import Optional, Dict, List, Tuple
import mss
from PIL import Image

class SignalAnalyzer:
    """信号分析器"""
    
    def __init__(self, ocr_manager, signal_region: Dict[str, int]):
        """
        初始化信号分析器
        
        Args:
            ocr_manager: OCR管理器实例
            signal_region: 信号区域配置 {'x': int, 'y': int, 'width': int, 'height': int}
        """
        self.logger = logging.getLogger(__name__)
        self.ocr_manager = ocr_manager
        self.signal_region = signal_region
        
        # 支持的信号类型（简化为单字识别）
        self.valid_signals = ['持', '开', '空', '清']
        
        # 单字到完整词汇的映射
        self.signal_mapping = {
            '持': '持仓',
            '开': '开仓', 
            '空': '空仓',
            '清': '清仓'
        }
        
        # OCR识别统计
        self.recognition_stats = {
            'total_attempts': 0,
            'successful_recognitions': 0,
            'failed_recognitions': 0,
            'invalid_signals': 0
        }
        
        self.logger.info(f"信号分析器初始化完成，信号区域: {signal_region}")
    
    def recognize_signal(self, stock_code: str) -> Optional[str]:
        """
        识别指定股票的信号
        
        Args:
            stock_code: 股票代码
            
        Returns:
            识别到的信号内容，失败返回None
        """
        self.recognition_stats['total_attempts'] += 1
        
        try:
            self.logger.debug(f"开始识别股票 {stock_code} 的信号")
            
            # 获取屏幕截图
            screenshot = self._capture_signal_region()
            if screenshot is None:
                self.logger.error(f"股票 {stock_code} 信号区域截图失败")
                self.recognition_stats['failed_recognitions'] += 1
                return None
            
            # OCR识别文字
            raw_text = self._perform_ocr(screenshot)
            if not raw_text:
                self.logger.warning(f"股票 {stock_code} OCR识别无结果")
                self.recognition_stats['failed_recognitions'] += 1
                return None
            
            # 清洗和解析文字
            cleaned_text = self.clean_ocr_text(raw_text)
            signal = self.parse_signal_content(cleaned_text)
            
            if self.validate_signal(signal):
                full_signal_name = self.get_full_signal_name(signal)
                self.logger.debug(f"股票 {stock_code} 信号识别成功: {signal} ({full_signal_name})")
                self.recognition_stats['successful_recognitions'] += 1
                return signal
            else:
                self.logger.warning(f"股票 {stock_code} 识别到无效信号: '{signal}' (原始: '{raw_text}')")
                self.recognition_stats['invalid_signals'] += 1
                return None
                
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 信号识别出错: {str(e)}")
            self.recognition_stats['failed_recognitions'] += 1
            return None
    
    def _capture_signal_region(self) -> Optional[Image.Image]:
        """
        截取信号区域
        
        Returns:
            PIL图像对象或None
        """
        try:
            with mss.mss() as sct:
                # 构建截图区域
                region = {
                    'left': self.signal_region['x'],
                    'top': self.signal_region['y'],
                    'width': self.signal_region['width'],
                    'height': self.signal_region['height']
                }
                
                # 截取指定区域
                screenshot = sct.grab(region)
                
                # 转换为PIL Image
                pil_image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                
                self.logger.debug(f"信号区域截图成功，尺寸: {pil_image.size}")
                return pil_image
                
        except Exception as e:
            self.logger.error(f"截取信号区域失败: {str(e)}")
            return None
    
    def _perform_ocr(self, image: Image.Image) -> str:
        """
        对图像执行OCR识别
        
        Args:
            image: PIL图像对象
            
        Returns:
            识别的文字内容
        """
        try:
            if not self.ocr_manager.is_initialized():
                self.logger.error("OCR管理器未初始化")
                return ""
            
            # 使用OCR管理器进行识别
            ocr_engine = self.ocr_manager.get_ocr_engine()
            if ocr_engine is None:
                self.logger.error("无法获取OCR引擎")
                return ""
            
            # 执行OCR识别
            if hasattr(ocr_engine, 'recognize_from_image'):
                results = ocr_engine.recognize_from_image(image)
            elif hasattr(ocr_engine, 'simple_engine') and hasattr(ocr_engine.simple_engine, 'recognize_image'):
                # 使用PaddleOCRCompatibilityWrapper内部的simple_engine
                import numpy as np
                import cv2
                
                # 将PIL图像转换为numpy数组
                image_array = np.array(image)
                # 转换RGB到BGR（OpenCV格式）
                if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                    image_array = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
                
                results = ocr_engine.simple_engine.recognize_image(image_array)
            else:
                # 最后的回退方案：保存临时图像后使用其他识别方法
                temp_path = f"/tmp/signal_temp_{int(time.time())}.png"
                image.save(temp_path)
                
                # 尝试使用test_raw_ocr_recognition方法
                if hasattr(ocr_engine, 'test_raw_ocr_recognition'):
                    # 获取图像尺寸
                    width, height = image.size
                    result = ocr_engine.test_raw_ocr_recognition(0, 0, width, height)
                    if result.get('success'):
                        results = result.get('texts', [])
                    else:
                        results = []
                else:
                    results = []
                
                import os
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            
            # 提取文字内容
            if isinstance(results, list) and results:
                text = ' '.join([str(result[1]) if isinstance(result, tuple) else str(result) for result in results])
            elif isinstance(results, str):
                text = results
            else:
                text = str(results) if results else ""
            
            self.logger.debug(f"OCR识别结果: '{text}'")
            return text.strip()
            
        except Exception as e:
            self.logger.error(f"OCR识别出错: {str(e)}")
            return ""
    
    def clean_ocr_text(self, raw_text: str) -> str:
        """
        清洗OCR识别结果
        
        Args:
            raw_text: OCR原始识别结果
            
        Returns:
            清洗后的文字
        """
        if not raw_text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', '', raw_text)
        
        # 常见OCR错误修正（针对单字）
        corrections = {
            # 持字相关错误
            '侍': '持',
            '挃': '持',
            
            # 开字相关错误
            '井': '开',
            '升': '开',
            '廾': '开',
            
            # 空字相关错误
            '宾': '空',
            '穹': '空',
            '室': '空',
            
            # 清字相关错误
            '青': '清',
            '请': '清',
            '情': '清'
        }
        
        # 应用修正规则
        for wrong, correct in corrections.items():
            text = text.replace(wrong, correct)
        
        self.logger.debug(f"OCR文字清洗: '{raw_text}' -> '{text}'")
        return text
    
    def parse_signal_content(self, cleaned_text: str) -> str:
        """
        解析清洗后的文字内容为标准信号（单字版本）
        
        Args:
            cleaned_text: 清洗后的文字
            
        Returns:
            标准化的信号名称（单字）
        """
        if not cleaned_text:
            return ""
        
        # 直接匹配有效信号（单字）
        for signal in self.valid_signals:
            if signal in cleaned_text:
                return signal
        
        # 模糊匹配策略（简化版本）
        text_lower = cleaned_text.lower()
        
        # 持字相关关键词
        if any(keyword in text_lower for keyword in ['持', 'chi']):
            return '持'
        
        # 开字相关关键词  
        if any(keyword in text_lower for keyword in ['开', 'kai']):
            return '开'
        
        # 空字相关关键词
        if any(keyword in text_lower for keyword in ['空', 'kong']):
            return '空'
        
        # 清字相关关键词
        if any(keyword in text_lower for keyword in ['清', 'qing']):
            return '清'
        
        self.logger.debug(f"无法解析信号内容: '{cleaned_text}'")
        return ""
    
    def validate_signal(self, signal: str) -> bool:
        """
        验证信号是否有效
        
        Args:
            signal: 待验证的信号
            
        Returns:
            是否为有效信号
        """
        return signal in self.valid_signals
    
    def get_full_signal_name(self, short_signal: str) -> str:
        """
        获取单字信号对应的完整名称
        
        Args:
            short_signal: 单字信号
            
        Returns:
            完整的信号名称
        """
        return self.signal_mapping.get(short_signal, short_signal)
    
    def get_recognition_stats(self) -> Dict[str, int]:
        """
        获取OCR识别统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.recognition_stats.copy()
        
        # 计算成功率
        if stats['total_attempts'] > 0:
            stats['success_rate'] = round(stats['successful_recognitions'] / stats['total_attempts'] * 100, 2)
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """重置识别统计"""
        self.recognition_stats = {
            'total_attempts': 0,
            'successful_recognitions': 0,
            'failed_recognitions': 0,
            'invalid_signals': 0
        }
        self.logger.info("OCR识别统计已重置")
    
    def test_signal_recognition(self) -> Dict[str, any]:
        """
        测试信号识别功能
        
        Returns:
            测试结果字典
        """
        self.logger.info("开始测试信号识别功能")
        
        try:
            # 截取当前信号区域
            screenshot = self._capture_signal_region()
            if screenshot is None:
                return {
                    'success': False,
                    'error': '无法截取信号区域',
                    'signal': None,
                    'raw_text': None
                }
            
            # 执行OCR识别
            raw_text = self._perform_ocr(screenshot)
            cleaned_text = self.clean_ocr_text(raw_text)
            signal = self.parse_signal_content(cleaned_text)
            is_valid = self.validate_signal(signal)
            
            result = {
                'success': True,
                'signal': signal if is_valid else None,
                'raw_text': raw_text,
                'cleaned_text': cleaned_text,
                'is_valid': is_valid,
                'region': self.signal_region,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self.logger.info(f"信号识别测试完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"信号识别测试失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'signal': None,
                'raw_text': None
            }