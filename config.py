# -*- coding: utf-8 -*-
"""
配置文件
包含所有可配置的变量，方便用户修改
"""

# 导入硬件相关配置（PC环境相关，独立管理）
try:
    from compass_hardware_config import (
        get_hardware_config,
        update_ocr_region as hw_update_ocr_region,
        update_buy_signal_region as hw_update_buy_signal_region,
        update_status_region as hw_update_status_region,
        update_mouse_target_position as hw_update_mouse_target_position
    )
except ImportError:
    print("警告: 无法导入硬件配置文件，将使用默认配置")
    # 提供默认配置作为备用
    def get_hardware_config(key=None):
        default_config = {
            'ocr_region': {'x': 450, 'y': 1115, 'width': 123, 'height': 81},
            'buy_signal_region': {'x': 338, 'y': 139, 'width': 61, 'height': 33},
            'status_region': {'x': 749, 'y': 144, 'width': 323, 'height': 24},
            'mouse_target_position': {'x': 2105, 'y': 521}
        }
        return default_config.get(key) if key else default_config
    
    def hw_update_ocr_region(*args): return False
    def hw_update_buy_signal_region(*args): return False
    def hw_update_status_region(*args): return False
    def hw_update_mouse_target_position(*args): return False

# 指南针软件基础配置（不包含硬件相关配置）
_COMPASS_BASE_CONFIG = {
    # 指南针软件的可执行文件路径（键盘操作模式下可选）
    # 注意：在键盘操作模式下，软件路径不是必需的，请手动启动指南针软件
    'exe_path': r'C:\Program Files\Compass\Compass.exe',  # 仅供参考，可能需要调整
    
    # 指南针软件的进程名称（用于检测软件是否运行）
    'process_name': 'WavMain.exe',
    
    # 指南针软件的主窗口标题（支持模糊匹配）
    'main_window_title': '指南针',  # 简化为关键词，支持模糊匹配
    
    # 以下配置在键盘操作模式下已不再使用，保留以备向后兼容
    'search_box': {'auto_id': 'SearchBox', 'control_type': 'Edit', 'class_name': 'Edit'},
    'search_button': {'auto_id': 'SearchButton', 'control_type': 'Button', 'class_name': 'Button'},
    'fund_flow_area': {
        'today': {'auto_id': 'TodayFundFlow', 'control_type': 'Text', 'class_name': 'Static'},
        'yesterday': {'auto_id': 'YesterdayFundFlow', 'control_type': 'Text', 'class_name': 'Static'},
        'day_before_yesterday': {'auto_id': 'DayBeforeYesterdayFundFlow', 'control_type': 'Text', 'class_name': 'Static'}
    }
}

# 动态合并硬件配置到指南针软件配置
COMPASS_SOFTWARE = _COMPASS_BASE_CONFIG.copy()
COMPASS_SOFTWARE.update(get_hardware_config())

# 页面加载检测配置
PAGE_LOAD_DETECTION_CONFIG = {
    # 是否启用页面加载检测
    'enabled': True,

    # 默认超时时间（秒）
    'default_timeout': 10.0,

    # 最大重试次数
    'max_retries': 5,

    # 检测间隔时间（秒）
    'check_interval': 0.3,

    # 颜色检测参数（基于颜色分析，不再使用OCR）
    'background_threshold': 50,      # 背景色亮度阈值（0-255，小于等于此值认为是背景）
    'text_threshold': 90,            # 文字亮度阈值（0-255，大于等于此值认为是文字）- 降低以识别灰白色加载文字
    'text_pixel_ratio': 0.025,      # 文字像素比例阈值（0.0-1.0，超过此比例认为有文字）

    # 是否启用调试模式
    'debug_mode': True,

    # 错误处理配置
    'error_handling': {
        # 截图失败时的重试次数
        'screenshot_retry_count': 2,

        # 颜色分析失败时的重试次数
        'color_analysis_retry_count': 2,

        # 是否在检测失败时跳过当前股票
        'skip_on_detection_failure': False,

        # 是否在超时时继续执行（True: 继续, False: 跳过）
        # 设置为False确保检测失败时快速响应，避免不必要的等待
        'continue_on_timeout': False
    },

    # 性能优化配置
    'performance': {
        # 是否启用快速检测模式（减少重试次数）
        'fast_detection_mode': False,

        # 是否缓存颜色分析结果
        'cache_analysis_results': False
    }
}

# 应用程序配置
APP_CONFIG = {
    # 启用OCR模式（True: 使用OCR, False: 使用控件读取）
    # OCR模式更稳定，不依赖复杂的控件定位
    'use_ocr_mode': True,
    
    # 启用多天数据获取功能（True: 通过LEFT键导航获取历史数据, False: 仅获取当前页面数据）
    'enable_multi_day_data': True,
    
    # 每次操作后的等待时间（秒）
    'wait_time': 2,
    
    # 页面加载等待时间（秒）
    'page_load_wait': 0.5,
    
    # 最大重试次数
    'max_retries': 3,
    
    # 默认的Excel文件路径
    'default_excel_path': '',
    
    # 支持的Excel文件扩展名
    'excel_extensions': ['.xlsx', '.xls'],
    
    # Excel文件拆分数量（默认拆分为3个文件）
    'excel_split_count': 2,
    
    # 日志级别
    'log_level': 'INFO',
    
    # 鼠标定位功能开关
    'enable_mouse_positioning': True,
    
    # 鼠标重置时是否执行左键点击
    'enable_mouse_click_on_reset': True,
    
    # 鼠标重置时Y坐标随机偏移设置（解决指南针软件显示问题）
    'mouse_reset_offset': {
        # 是否启用Y坐标随机偏移
        'enable_y_offset': True,
        # Y坐标偏移范围（像素，正负值表示上下偏移）
        'y_offset_range': 20
    },

    # 页面加载检测配置（引用全局配置）
    'page_load_detection': PAGE_LOAD_DETECTION_CONFIG,

    # OCR设置
    'ocr_settings': {
        # OCR引擎模式选择
        # 'complex': 使用复杂的多引擎多策略OCR系统
        # 'simple_paddleocr': 使用简化的PaddleOCR引擎（推荐）
        'engine_mode': 'simple_paddleocr',

        # 强制使用CPU模式（PaddleOCR 3.0兼容性）
        'use_gpu': False,

        # 是否启用调试模式
        'debug_mode': True,

        # 是否保存调试图像
        'save_debug_images': True,

        # OCR图像缩放模式：'original'(原图), '2x'(2倍图), '4x'(4倍图)
        'image_scale_mode': 'original',

        # 本地模型配置
        'local_models': {
            'enabled': True,                    # 启用本地模型
            'models_dir': 'models',            # 模型目录
            'models': {
                'detection': {
                    'model_name': 'PP-OCRv5_mobile_det',
                    'enabled': True
                },
                'recognition': {
                    'model_name': 'PP-OCRv5_mobile_rec', 
                    'enabled': True
                }
            }
        },
        
        # 最大错误次数
        'max_errors': 10,
        
        # OCR置信度阈值
        'confidence_threshold': 0.5,
        
        # 调试图像保存目录
        'debug_images_dir': 'ocr_debug_images',
        
        # 支持的OCR引擎
        'engines': {
            'easyocr': {
                'enabled': True,
                'languages': ['ch_sim', 'en'],
                'gpu': True
            },
            'paddleocr': {
                'enabled': True,
                'use_angle_cls': True,
                'lang': 'ch'
            }
        },
        
        # 百分比识别专用配置
        'percentage_recognition': {
            # 是否启用百分比专用识别
            'enabled': True,
            
            # 百分比识别的置信度阈值（更宽松）
            'confidence_threshold': 0.001,
            
            # 预处理策略
            'preprocessing_strategies': {
                'standard': {
                    'enabled': True,
                    'scale_factor': 5.0,
                    'alpha': 1.2,
                    'beta': 10
                },
                'conservative': {
                    'enabled': True,
                    'scale_factor': 3.0,
                    'sharpening_strength': 0.3
                },
                'aggressive': {
                    'enabled': True,
                    'scale_factor': 3.0,
                    'alpha': 1.3,
                    'beta': 15
                },
                'scale_only': {
                    'enabled': True,
                    'scale_factor': 4.0
                }
            },
            
            # 文本解析设置
            'text_parsing': {
                # 百分比值的合理范围
                'reasonable_range': {
                    'min': -50.0,
                    'max': 50.0
                },
                
                # OCR错误修正规则
                'error_correction': {
                    'enabled': True,
                    'rules': [
                        {'pattern': r'#', 'replacement': '%', 'description': '特殊字符修正'},
                        {'pattern': r'％', 'replacement': '%', 'description': '中文百分号修正'},
                        {'pattern': r'[oO]', 'replacement': '0', 'description': '字母O修正为数字0'},
                        {'pattern': r'^[I|l1]\\s*', 'replacement': '-', 'description': '负号修正'}
                    ]
                },
                
                # 正则表达式模式
                'regex_patterns': [
                    r'([+-]?\d+\.?\d*)\s*%',
                    r'([+-]?\d+\.?\d*)\s*％',
                    r'([+-]?\d+\.?\d*)\s*percent',
                    r'([+-]?\d+\.?\d*)\s*pct',
                    r'([+-]?\d+)\s+(\d+)\s*%'
                ]
            },
            
            # EasyOCR专用参数 - 优化版
            'easyocr_params': {
                'allowlist': '0123456789.-%+',
                'width_ths': 0.2,      # 降低宽度阈值，提高小字符识别
                'height_ths': 0.2,     # 降低高度阈值，提高小字符识别
                'paragraph': False,
                'detail': 1,           # 返回详细信息
                'decoder': 'greedy',   # 使用贪婪解码
                'beamWidth': 5,        # 增加beam width
                'batch_size': 1,       # 小批量处理
                'workers': 0,          # 单线程处理，避免冲突
                'rotation_info': None  # 不进行旋转检测
            },

            # PaddleOCR专用参数 - 优化版
            'paddleocr_params': {
                'use_angle_cls': False,    # 不使用角度分类
                'use_gpu': True,           # 使用GPU加速
                'det_db_thresh': 0.2,      # 降低检测阈值
                'det_db_box_thresh': 0.5,  # 边框阈值
                'det_db_unclip_ratio': 1.6, # 扩展比例
                'rec_batch_num': 1,        # 识别批量大小
                'max_text_length': 25,     # 最大文本长度
                'rec_char_dict_path': None, # 使用默认字典
                'use_space_char': False,   # 不使用空格字符
                'drop_score': 0.3          # 丢弃低分结果
            }
        }
    },
    
    # 信号识别设置
    'signal_recognition': {
        # 信号识别模式选择
        # 'color': 纯颜色识别模式（最快）
        # 'ocr': 纯OCR识别模式（传统）
        # 'hybrid': 混合模式，优先颜色识别，失败时回退到OCR（推荐）
        'recognition_mode': 'hybrid',
        
        # 颜色识别配置
        'color_recognition': {
            # 是否启用颜色识别
            'enabled': True,
            
            # 颜色模式配置（四种信号的标准颜色）
            'color_patterns': {
                '开仓': {
                    'rgb': (255, 0, 0),      # 红色
                    'tolerance': 40,          # 容差值
                    'min_confidence': 0.3     # 最小置信度
                },
                '清仓': {
                    'rgb': (0, 255, 142),    # 青绿色（更接近实际）
                    'tolerance': 60,          # 增加容差以适应青绿色变化
                    'min_confidence': 0.3
                },
                '空仓': {
                    'rgb': (160, 160, 160),  # 调整为更接近实际的灰白色
                    'tolerance': 70,          # 增加容差以适应抗锯齿和压缩失真
                    'min_confidence': 0.3     # 降低最小置信度
                },
                '持仓': {
                    'rgb': (255, 165, 0),    # 橙色
                    'tolerance': 45,
                    'min_confidence': 0.3
                }
            },
            
            # 颜色校准配置
            'calibration': {
                # 是否启用自动校准
                'auto_calibration': True,
                
                # 校准样本数量
                'sample_count': 10,
                
                # 校准置信度阈值
                'calibration_threshold': 0.8
            }
        }
    }
}

# GUI配置
GUI_CONFIG = {
    # 主窗口配置
    'main_window': {
        'title': '股票筛选器 - 指南针数据分析',
        'width': 1200,
        'height': 550,
        'min_width': 1000,
        'min_height': 400
    },
    
    # 表格配置
    'table': {
        'columns': ['序号', '股票代码', '今日资金', '昨日资金', '前日资金', '状态'],
        'column_widths': [50, 85, 100, 100, 100, 70]
    },
    
    # 状态栏配置
    'status_bar': {
        'ready_message': '准备就绪',
        'processing_message': '正在处理中...',
        'completed_message': '分析完成'
    }
}

# OCR配置（向后兼容）
OCR_CONFIG = {
    # EasyOCR支持的语言
    'languages': ['ch_sim', 'en'],
    
    # OCR置信度阈值
    'confidence_threshold': 0.5,
    
    # 是否使用GPU加速
    'use_gpu': False,
    
    # 调试设置
    'debug_settings': {
        # 是否启用调试模式
        'enabled': False,
        
        # 是否保存调试图像
        'save_images': False,
        
        # 调试日志级别
        'log_level': 'INFO',
        
        # 图像预处理参数
        'preprocessing': {
            # 亮度调整
            'brightness_alpha': 1.5,
            'brightness_beta': 30,
            
            # 高斯滤波参数
            'gaussian_blur_kernel': (3, 3),
            'gaussian_blur_sigma': 0,
            
            # 形态学操作内核
            'morphology_kernel_size': (2, 2),
            
            # 阈值处理参数
            'adaptive_threshold_max_value': 255,
            'adaptive_threshold_block_size': 11,
            'adaptive_threshold_c': 2
        }
    },
    
    # 错误处理设置
    'error_handling': {
        # 最大错误次数
        'max_errors': 10,
        
        # 错误后的重试延迟（秒）
        'retry_delay': 1,
        
        # 是否启用备用识别方案
        'use_backup_methods': True
    },
    
    # 多引擎设置
    'multi_engine': {
        # 是否启用多引擎识别
        'enabled': True,
        
        # 引擎优先级（按顺序尝试）
        'priority': ['easyocr', 'paddleocr'],
        
        # 结果合并策略
        'merge_strategy': 'priority'  # 'priority' 或 'consensus'
    }
}

# 数据筛选配置
FILTER_CONFIG = {
    # 筛选条件：今天 > 昨天 > 前天
    'filter_logic': 'today > yesterday > day_before_yesterday',
    
    # 数据验证：是否检查数据的合理性
    'validate_data': True,
    
    # 允许的数据范围（用于验证）
    'data_range': {
        'min_value': -999999,
        'max_value': 999999
    }
}

# 统一信号监控配置
SIGNAL_CONFIG = {
    # 支持的信号类型
    'signals': ['持', '开', '空', '清'],

    # 页面切换等待时间（秒）
    'page_switch_wait': 1,

    # 轮询间隔时间（秒）
    'round_interval': 1.0,

    # 开盘热身功能：每只股票首次跳过信号识别，避免读取到昨日数据
    # 指南针软件在首次加载股票时会先显示前一交易日数据，然后跳转到当日
    # 启用此功能可确保首次识别的信号是当日真实信号
    'enable_stock_warmup': True,

    # OCR重试次数
    'ocr_retry_count': 2,

    # 是否忽略错误继续执行
    'ignore_errors': True,

    # 是否记录所有信号（包括无变化的）
    'log_all_signals': False,

    # 是否仅记录信号变化
    'log_signal_changes_only': True,

    # 监控统计设置
    'enable_statistics': True,

    # 信号识别超时时间（秒）
    'recognition_timeout': 5.0,

    # 页面加载检测配置（引用全局配置）
    'page_load_detection': PAGE_LOAD_DETECTION_CONFIG,

    # 错误重试策略
    'error_handling': {
        'max_consecutive_errors': 3,
        'error_cooldown_time': 2.0,
        'skip_after_max_errors': True
    },

    # OCR识别优化参数
    'ocr_optimization': {
        'use_signal_specific_preprocessing': True,
        'signal_confidence_threshold': 0.6,
        'prefer_simplified_ocr': True
    },

    # 15:01自动停止功能
    'auto_stop_at_market_close': True,  # 是否在每天15:01后自动停止监控

    # 钉钉消息严格模式：True=只在主要信号时发送通知，False=保持现有逻辑
    'strict_notification_mode': True,  # 默认为严格模式

    # 信号类型特定配置
    'signal_type_configs': {
        'buy': {
            'highlight_open_signal': True,  # 开仓信号特殊标记
            'primary_signal': '开仓',       # 主要关注的信号
            'alert_signals': ['开仓'],      # 需要提醒的信号
        },
        'sell': {
            'highlight_sell_signal': True,  # 清仓信号特殊标记
            'danger_alert_enabled': True,   # 危险信号提醒
            'primary_signal': '清仓',       # 主要关注的信号
            'alert_signals': ['清仓'],      # 需要提醒的信号
        }
    }
}

# 为了向后兼容，保留原有的配置变量（指向统一配置）
BUY_SIGNAL_CONFIG = SIGNAL_CONFIG.copy()
BUY_SIGNAL_CONFIG.update(SIGNAL_CONFIG['signal_type_configs']['buy'])

SELL_SIGNAL_CONFIG = SIGNAL_CONFIG.copy()  
SELL_SIGNAL_CONFIG.update(SIGNAL_CONFIG['signal_type_configs']['sell'])

# 钉钉机器人群配置
DINGTALK_GROUPS = {
    '💬 红盘起爆': {
        'webhook_url': 'https://oapi.dingtalk.com/robot/send?access_token=2748d33e3dff06ff5f17bee1d18edc4a4a1e95cd8a45fb3bf3bf30e7afbb65d3',
        'secret': 'SEC4e904956746d749935baba560bf3b575b6eb11f3bd4c1878cbe45c214a24a023'
    },
    '💬 奔向富足': {
        'webhook_url': 'https://oapi.dingtalk.com/robot/send?access_token=f5a2932e5e12fd08333e71db1222ad022ec126888e928bd53fe1bb1a03089f87',
        'secret': 'SECdb5658bf154a598d44c9402ee291a540ef9d7e4ae184969f75f50d89b0c6c997'
    },
    '💬 持仓股': {
        'webhook_url': 'https://oapi.dingtalk.com/robot/send?access_token=20f47426c3ca51c3b4a8d2169c0fb667a03ef65a8a73fc83aae33574e3170771',
        'secret': 'SEC963724ab6ea4df2ed5410f9e86180e41da61e7137732e697ca2e952b7a5b5295'
    },
    '💬 量比群': {
        'webhook_url': 'https://oapi.dingtalk.com/robot/send?access_token=17f7dd3274b715b9f89fbf663d74ac222e7ce6677e6e6679c2124f6da5761e72',
        'secret': 'SEC451b5ac582b98d17e8e701e2b750ccaffbce9b9b53892c1f419616826040ec4c'
    }
}

# 钉钉机器人通知配置
DINGTALK_CONFIG = {
    # 当前选择的钉钉群
    'selected_group': '💬 奔向富足',
    
    # 钉钉机器人webhook URL 
    'webhook_url': 'https://oapi.dingtalk.com/robot/send?access_token=f5a2932e5e12fd08333e71db1222ad022ec126888e928bd53fe1bb1a03089f87',
    
    # 钉钉机器人加签密钥
    'secret': 'SECdb5658bf154a598d44c9402ee291a540ef9d7e4ae184969f75f50d89b0c6c997',
    
    # 是否启用钉钉通知
    'enabled': True,
    
    # 通知设置
    'notification_settings': {
        # 是否通知首次开仓信号
        'notify_first_open_signal': True,
        
        # 是否通知所有信号变化
        'notify_all_signal_changes': True,
        
        # 是否仅通知开仓相关变化
        'notify_open_signal_only': False,
        
        # 通知频率限制（秒）- 同一股票在此时间内只发送一次通知
        'notification_cooldown': 10,
        
        # 是否@所有人
        'at_all': False,
        
        # 重试设置
        'retry_count': 2,
        'retry_delay': 1.0,
        
        # 超时设置（秒）
        'timeout': 10
    },
    
    # 消息模板设置
    'message_templates': {
        # 首次开仓信号模板
        'first_open_signal': '🚀 股票信号提醒\n\n📈 股票代码: {stock_code}\n🎯 信号状态: {signal}\n⏰ 时间: {time}\n🔔 类型: 首次检测到开仓信号',
        
        # 信号变化为开仓模板
        'change_to_open': '🚀 股票信号变化提醒\n\n📈 股票代码: {stock_code}\n🔄 信号变化: {old_signal} → {signal}\n⏰ 时间: {time}\n🎯 重要: 出现开仓信号！',
        
        # 其他信号变化模板
        'signal_change': '📊 股票信号变化提醒\n\n📈 股票代码: {stock_code}\n🔄 信号变化: {old_signal} → {signal}\n⏰ 时间: {time}\n📝 类型: 信号状态变化',
        
        # 测试连接模板
        'test_connection': '🤖 钉钉机器人连接测试\n\n⏰ 测试时间: {time}\n✅ 股票监控系统钉钉通知功能正常'
    }
}

# 网页自动化配置
WEB_AUTOMATION_CONFIG = {
    # 目标网站
    'target_url': 'https://www.topxlc.com/ddcj-yqs-xc/web/',
    
    # 目标数据字段
    'target_fields': [
        '小草竞王',
        '小草红盘起爆', 
        '小草绿盘低吸',
        '小草连板接力'
    ],
    
    # 指标筛选配置
    'indicator_filtering': {
        # 指标选择器配置
        'indicator_options': [
            '小草红盘起爆',  # 默认选项
            '小草竞王',
            '小草绿盘低吸',
            '小草连板接力'
        ],
        
        # 默认选择的指标
        'default_indicator': '小草红盘起爆',
        
        # 指标名称到内部字段的映射
        'indicator_mapping': {
            '小草竞王': 'jingwang',
            '小草红盘起爆': 'hongpan_qibao',
            '小草绿盘低吸': 'lvpan_dixi',
            '小草连板接力': 'lianban_jieli'
        },
        
        # 指标字段到表格列索引的映射（基于HTML结构）
        'field_to_column': {
            'jingwang': 12,         # 小草竞王列索引
            'hongpan_qibao': 13,    # 小草红盘起爆列索引
            'lvpan_dixi': 14,       # 小草绿盘低吸列索引
            'lianban_jieli': 11     # 小草连板接力列索引
        },
        
        # 滚动停止条件配置
        'scroll_stop_conditions': {
            # 指标值全部为0时停止滚动
            'stop_when_all_zero': True,
            
            # 指标值阈值：小于等于此值视为0
            'zero_threshold': 0.01,
            
            # 连续检查次数：连续N次检查均为0才停止
            'consecutive_zero_count': 2,
            
            # 最大滚动次数（防止无限滚动）
            'max_scroll_count': 200,
            
            # 滚动停止检查间隔（秒）
            'check_interval': 2.0
        },
        
        # 指标筛选输出配置
        'output_config': {
            # 输出文件名模板（包含指标信息）
            'filename_template': '小草{indicator}_筛选结果_{timestamp}.xlsx',
            
            # 工作表名称模板
            'sheet_name_template': '{indicator}筛选结果',
            
            # 是否在结果中包含所有指标列
            'include_all_indicators': True,
            
            # Excel文件分拆配置
            'file_splitting': {
                # 是否启用文件分拆功能
                'enabled': True,
                
                # 分拆阈值：此配置已弃用，分拆功能现在完全由split_count配置控制
                'split_threshold': 30,
                
                # 分拆文件数量（默认拆分为3个文件）
                'split_count': 3,
                
                # 分拆文件命名后缀模板
                'part_suffix_template': '_part{part_number}',
                
                # 最大分拆文件数量（防止无限分拆）
                'max_parts': 10
            }
        },
        
        # 指标有效性验证配置
        'value_validation': {
            # 有效指标值的最小阈值
            'min_valid_value': 0.01,
            
            # 无效值列表（扩展版本）
            'invalid_values_extended': [
                '--', '0.00', '0.0', '0', '', ' ', 
                'null', 'undefined', 'N/A', '-', 'NaN'
            ],
            
            # 是否严格验证数值格式
            'strict_numeric_validation': True
        }
    },
    
    # 输出文件配置
    'output_file_format': '小草选股数据_{timestamp}.xlsx',
    'output_directory': 'web_automation_results',
    
    # 浏览器配置
    'browser_headless': False,
    'browser_timeout': 30000,
    
    # 页面等待时间（秒）
    'page_load_wait': 2,
    
    # 筛选条件：只保存有分值的股票
    'filter_empty_values': True,
    
    # 无效数据值列表：这些值被视为无效数据，在筛选时会被排除
    'invalid_values': ['--', '0.00', '', ' ', 'null', 'undefined', 'N/A', '-'],
    
    # 滚动加载配置
    'scroll_loading': {
        # 是否启用滚动加载（处理滚动分页的数据）
        'enabled': True,
        
        # 滚动策略配置（针对虚拟滚动优化）
        'scroll_strategy': {
            # 每次滚动的距离（像素）- 减小距离确保更细致的滚动
            'scroll_distance': 400,
            
            # 每次滚动后的等待时间（秒）- 增加等待确保数据完全加载
            'scroll_wait_time': 4.0,
            
            # 数据稳定检测：连续N次滚动无新数据则认为加载完成（减少要求）
            'stable_check_count': 6,
            
            # 最大滚动次数（防止无限滚动）- 大幅增加以应对大量数据
            'max_scroll_attempts': 500,
            
            # 每次数据变化检测的等待时间（秒）- 增加等待确保DOM更新
            'data_change_wait': 3.0
        },
        
        # 数据完整性检测配置
        'data_integrity': {
            # 最小数据行数（少于此数量认为数据加载不完整）- 降低期望因为是虚拟滚动
            'min_data_rows': 5,
            
            # 数据加载超时时间（秒）- 大幅增加以应对大量数据
            'loading_timeout': 900,  # 15分钟
            
            # 单次滚动超时时间（秒）
            'single_scroll_timeout': 20
        },
        
        # 进度反馈配置
        'progress_feedback': {
            # 是否显示滚动进度
            'show_progress': True,
            
            # 进度更新频率：每N次滚动更新一次（虚拟滚动时更频繁更新）
            'update_frequency': 1,
            
            # 进度消息模板（更适合虚拟滚动的信息显示）
            'progress_template': '正在滚动加载数据... 第{scroll_count}次滚动，滚动位置{scroll_position}px'
        },
        
        # 虚拟滚动特殊配置
        'virtual_scroll_optimization': {
            # 启用虚拟滚动优化模式
            'enabled': True,
            
            # 虚拟滚动元素选择器配置
            'selectors': {
                # 虚拟滚动容器（定义总高度）
                'virtual_group': '.c-virtual-group',
                # 虚拟滚动偏移元素（滚动进度）
                'virtual_before': '.c-virtual-before',
                # 数据行选择器
                'data_rows': '.c-table-row-body'
            },
            
            # 滚动位置变化的最小阈值（像素）
            'position_change_threshold': 50,
            
            # 接近底部的距离阈值（像素）
            'bottom_threshold': 200,
            
            # 强制滚动到绝对底部的尝试次数
            'force_bottom_attempts': 5,
            
            # 虚拟滚动检测配置
            'detection': {
                # 虚拟滚动进度阈值（百分比）- 降低要求确保不会过早退出
                'progress_threshold': 98.0,
                # 最小虚拟内容高度（像素）- 降低要求适应不同页面
                'min_virtual_height': 500,
                # 连续稳定次数 - 减少要求提高获取成功率
                'stable_count_multiplier': 1.0
            },
            
            # 调试诊断配置
            'debug_diagnostics': {
                # 启用虚拟滚动调试诊断模式
                'enabled': True,
                # 输出详细的虚拟滚动检测信息
                'verbose_detection': True,
                # 输出每次滚动的详细状态
                'verbose_scroll_info': True,
                # 输出虚拟滚动元素的详细属性
                'verbose_element_info': True,
                # 保存虚拟滚动诊断日志到文件
                'save_diagnostic_log': False,
                # 诊断日志文件路径
                'diagnostic_log_file': 'virtual_scroll_diagnostic.log'
            }
        }
    },
    
    # 自动登录配置
    'auto_login': {
        # 是否启用自动登录
        'enabled': True,
        
        # 登录方式：'button' = 点击登录按钮, 'enter' = 按回车键, 'auto' = 智能选择
        'login_method': 'auto',
        
        # 登录按钮等待超时时间（毫秒）
        'login_button_timeout': 5000,
        
        # 登录操作后等待时间（秒）
        'login_wait_time': 2,
        
        # 登录结果检测等待时间（秒）
        'login_detection_wait': 1,
        
        # 登录超时时间（秒）
        'login_timeout': 30,
        
        # 是否启用登录结果检测
        'enable_login_detection': True,
        
        # 登录失败后的重试次数
        'login_retry_count': 1,
        
        # 登录失败重试间隔（秒）
        'login_retry_interval': 3
    },
    
    # 网络请求监听配置
    'network_monitoring': {
        # 是否启用网络请求监听
        'enabled': True,
        
        # API请求模式匹配
        'api_patterns': [
            '**/api/**',
            '**/data/**', 
            '**/*stock*',
            '**/*list*',
            '**/*page*',
            '**/*search*'
        ],
        
        # 网络请求超时配置
        'timeouts': {
            # 单个请求超时时间（秒）
            'single_request_timeout': 10,
            # 等待网络空闲超时时间（秒）
            'network_idle_timeout': 5
        },
        
        # 请求状态码过滤
        'valid_status_codes': [200, 201, 202, 204],
        
        # 请求内容类型过滤
        'content_types': [
            'application/json',
            'text/html',
            'application/javascript'
        ]
    },
    
    # 页面选择器（待实际测试后配置）
    'selectors': {
        # 这些选择器需要在看到实际页面后配置
        'data_table': '',
        'stock_rows': '',
        'stock_code_column': '',
        'stock_name_column': '',
        'target_field_columns': {}
    }
}

# 配置文件动态更新函数
def update_ocr_region_config(x: int, y: int, width: int, height: int):
    """
    动态更新OCR区域配置并写入配置文件
    
    Args:
        x: 区域左上角X坐标（绝对屏幕坐标）
        y: 区域左上角Y坐标（绝对屏幕坐标）
        width: 区域宽度
        height: 区域高度
    """
    try:
        # 调用硬件配置文件中的更新函数
        result = hw_update_ocr_region(x, y, width, height)
        
        # 更新内存中的配置
        if result:
            COMPASS_SOFTWARE['ocr_region'] = {
                'x': x,
                'y': y,
                'width': width,
                'height': height
            }
        
        return result
        
    except Exception as e:
        print(f"更新OCR区域配置失败: {str(e)}")
        return False

def update_buy_signal_region_config(x: int, y: int, width: int, height: int):
    """
    动态更新买入信号区域配置并写入配置文件
    
    Args:
        x: 区域左上角X坐标（绝对屏幕坐标）
        y: 区域左上角Y坐标（绝对屏幕坐标）
        width: 区域宽度
        height: 区域高度
    """
    try:
        # 调用硬件配置文件中的更新函数
        result = hw_update_buy_signal_region(x, y, width, height)
        
        # 更新内存中的配置
        if result:
            COMPASS_SOFTWARE['buy_signal_region'] = {
                'x': x,
                'y': y,
                'width': width,
                'height': height
            }
        
        return result
        
    except Exception as e:
        print(f"更新买入信号区域配置失败: {str(e)}")
        return False

def update_status_region_config(x: int, y: int, width: int, height: int):
    """
    动态更新状态区域配置并写入配置文件
    
    Args:
        x: 区域左上角X坐标（绝对屏幕坐标）
        y: 区域左上角Y坐标（绝对屏幕坐标）
        width: 区域宽度
        height: 区域高度
    """
    try:
        # 调用硬件配置文件中的更新函数
        result = hw_update_status_region(x, y, width, height)
        
        # 更新内存中的配置
        if result:
            COMPASS_SOFTWARE['status_region'] = {
                'x': x,
                'y': y,
                'width': width,
                'height': height
            }
        
        return result
        
    except Exception as e:
        print(f"更新状态区域配置失败: {str(e)}")
        return False

def update_mouse_target_position_config(x: int, y: int):
    """
    动态更新鼠标基准位置配置并写入配置文件
    
    Args:
        x: 鼠标基准位置X坐标（绝对屏幕坐标）
        y: 鼠标基准位置Y坐标（绝对屏幕坐标）
    """
    try:
        # 调用硬件配置文件中的更新函数
        result = hw_update_mouse_target_position(x, y)
        
        # 更新内存中的配置
        if result:
            COMPASS_SOFTWARE['mouse_target_position'] = {
                'x': x,
                'y': y
            }
        
        return result
        
    except Exception as e:
        print(f"更新鼠标基准位置配置失败: {str(e)}")
        return False

def update_mouse_positioning_config(enabled: bool):
    """
    动态更新鼠标定位功能配置并写入配置文件
    
    Args:
        enabled: 是否启用鼠标定位功能
    """
    import os
    import re
    
    try:
        # 更新内存中的配置
        APP_CONFIG['enable_mouse_positioning'] = enabled
        
        # 读取当前配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'config.py')
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换enable_mouse_positioning的值
        pattern = r"('enable_mouse_positioning':\s*)(?:True|False)"
        replacement = f"'enable_mouse_positioning': {enabled}"
        
        new_content = re.sub(pattern, replacement, content)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        return True
        
    except Exception as e:
        print(f"更新鼠标定位功能配置文件失败: {str(e)}")
        return False

def update_auto_stop_config(enabled: bool):
    """
    动态更新自动停止信号检测配置并写入配置文件
    同时更新统一信号配置和买入、卖出信号的auto_stop_at_market_close配置
    
    Args:
        enabled: 是否启用15:01自动停止功能
    """
    import os
    import re
    
    try:
        # 更新内存中的配置（包括统一配置）
        SIGNAL_CONFIG['auto_stop_at_market_close'] = enabled
        BUY_SIGNAL_CONFIG['auto_stop_at_market_close'] = enabled
        SELL_SIGNAL_CONFIG['auto_stop_at_market_close'] = enabled
        
        # 读取当前配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'config.py')
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换所有的auto_stop_at_market_close值
        pattern = r"('auto_stop_at_market_close':\s*)(?:True|False)"
        replacement = f"'auto_stop_at_market_close': {enabled}"
        
        new_content = re.sub(pattern, replacement, content)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        return True
        
    except Exception as e:
        print(f"更新自动停止信号检测配置文件失败: {str(e)}")
        return False

def update_excel_split_count_config(count: int):
    """
    动态更新Excel文件拆分数量配置并写入配置文件
    
    Args:
        count: 拆分文件数量（2-10之间的整数）
    """
    import os
    import re
    
    try:
        # 输入验证
        if not isinstance(count, int) or count < 2 or count > 10:
            print(f"拆分数量必须是2-10之间的整数，当前值: {count}")
            return False
        
        # 更新内存中的配置
        APP_CONFIG['excel_split_count'] = count
        
        # 读取当前配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'config.py')
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换excel_split_count的值
        pattern = r"('excel_split_count':\s*)\d+"
        replacement = f"'excel_split_count': {count}"
        
        new_content = re.sub(pattern, replacement, content)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        return True
        
    except Exception as e:
        print(f"更新Excel拆分数量配置文件失败: {str(e)}")
        return False

def update_strict_notification_mode_config(enabled: bool):
    """
    动态更新钉钉消息严格模式配置并写入配置文件
    
    Args:
        enabled: 是否启用严格模式（True=只在主要信号时发送通知，False=保持现有逻辑）
    """
    import os
    import re
    
    try:
        # 更新内存中的配置
        SIGNAL_CONFIG['strict_notification_mode'] = enabled
        
        # 读取当前配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'config.py')
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换strict_notification_mode的值
        pattern = r"('strict_notification_mode':\s*)(?:True|False)"
        replacement = f"'strict_notification_mode': {enabled}"
        
        new_content = re.sub(pattern, replacement, content)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        print(f"钉钉消息严格模式配置已更新为: {'启用' if enabled else '禁用'}")
        return True
        
    except Exception as e:
        print(f"更新钉钉消息严格模式配置文件失败: {str(e)}")
        return False

def update_indicator_split_count_config(count: int):
    """
    动态更新网页指标拆分数量配置并写入配置文件
    
    Args:
        count: 拆分文件数量（2-10之间的整数）
    """
    import os
    import re
    
    try:
        # 输入验证
        if not isinstance(count, int) or count < 2 or count > 10:
            print(f"指标拆分数量必须是2-10之间的整数，当前值: {count}")
            return False
        
        # 更新内存中的配置
        WEB_AUTOMATION_CONFIG['indicator_filtering']['output_config']['file_splitting']['split_count'] = count
        
        # 读取当前配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'config.py')
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换file_splitting配置中的split_count值
        # 需要更精确地匹配file_splitting配置块内的split_count
        pattern = r"('file_splitting':\s*\{[^}]*?'split_count':\s*)\d+"
        replacement = f"\\g<1>{count}"
        
        new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        return True
        
    except Exception as e:
        print(f"更新网页指标拆分数量配置文件失败: {str(e)}")
        return False

def update_dingtalk_group_selection(group_name: str):
    """
    动态更新钉钉群选择配置并写入配置文件
    
    Args:
        group_name: 选择的钉钉群名称
    """
    import os
    import re
    
    try:
        # 验证群名称是否有效
        if group_name not in DINGTALK_GROUPS:
            print(f"无效的钉钉群名称: {group_name}")
            return False
        
        # 获取群配置
        group_config = DINGTALK_GROUPS[group_name]
        webhook_url = group_config['webhook_url']
        secret = group_config['secret']
        
        # 更新内存中的配置
        DINGTALK_CONFIG['selected_group'] = group_name
        DINGTALK_CONFIG['webhook_url'] = webhook_url
        DINGTALK_CONFIG['secret'] = secret
        
        # 读取当前配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'config.py')
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新DINGTALK_CONFIG中的selected_group值（使用更精确的模式）
        pattern = r"(DINGTALK_CONFIG\s*=\s*\{[^}]*?'selected_group':\s*)'[^']*'"
        replacement = f"\\g<1>'{group_name}'"
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # 更新DINGTALK_CONFIG中的webhook_url值（使用更精确的模式）  
        pattern = r"(DINGTALK_CONFIG\s*=\s*\{[^}]*?'webhook_url':\s*)'[^']*'"
        replacement = f"\\g<1>'{webhook_url}'"
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # 更新DINGTALK_CONFIG中的secret值（使用更精确的模式）
        pattern = r"(DINGTALK_CONFIG\s*=\s*\{[^}]*?'secret':\s*)'[^']*'"
        replacement = f"\\g<1>'{secret}'"
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"钉钉群配置已更新为: {group_name}")
        return True
        
    except Exception as e:
        print(f"更新钉钉群选择配置文件失败: {str(e)}")
        return False