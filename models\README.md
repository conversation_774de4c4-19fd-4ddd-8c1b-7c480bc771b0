# PaddleOCR 本地模型目录

本目录用于存放 PaddleOCR 的本地模型文件，以提升启动速度和确保版本一致性。

## 目录结构

```
models/
├── PP-OCRv5_mobile_det/         # 检测模型
│   ├── inference.json           # 模型配置文件
│   ├── inference.pdiparams      # 模型参数文件
│   └── inference.yml            # 模型描述文件
├── PP-OCRv5_mobile_rec/         # 识别模型  
│   ├── inference.json           # 模型配置文件
│   ├── inference.pdiparams      # 模型参数文件
│   └── inference.yml            # 模型描述文件
├── PP-OCRv5_mobile_cls/         # 分类模型（可选）
│   ├── inference.json           # 模型配置文件
│   ├── inference.pdiparams      # 模型参数文件
│   └── inference.yml            # 模型描述文件
└── README.md                    # 本说明文件
```

## 推荐模型

### 针对48x48小图片数字识别的最佳配置

**优先推荐：PP-OCRv5 Mobile**
- **PP-OCRv5_mobile_det**: 轻量化检测模型
- **PP-OCRv5_mobile_rec**: 轻量化识别模型

### 模型特点对比

| 模型版本 | 模型大小 | 推理速度 | 准确率 | 适用场景 |
|---------|---------|---------|--------|----------|
| PP-OCRv5_mobile | ~10MB | 快 | 高 | 小图片数字识别（推荐） |
| PP-OCRv4_mobile | ~8MB | 较快 | 中高 | 资源受限环境 |
| PP-OCRv3_mobile | ~3MB | 最快 | 中 | 极速识别需求 |

## 模型下载

### 下载地址

#### HuggingFace（推荐）
- 检测模型: https://huggingface.co/PaddlePaddle/PP-OCRv5_mobile_det
- 识别模型: https://huggingface.co/PaddlePaddle/PP-OCRv5_mobile_rec

#### PaddleOCR 官方
- 检测模型: https://paddleocr.bj.bcebos.com/PP-OCRv5/mobile/PP-OCRv5_mobile_det_infer.tar
- 识别模型: https://paddleocr.bj.bcebos.com/PP-OCRv5/mobile/PP-OCRv5_mobile_rec_infer.tar

### 下载步骤

1. **下载压缩包**
   ```bash
   # 检测模型
   wget https://paddleocr.bj.bcebos.com/PP-OCRv5/mobile/PP-OCRv5_mobile_det_infer.tar
   
   # 识别模型  
   wget https://paddleocr.bj.bcebos.com/PP-OCRv5/mobile/PP-OCRv5_mobile_rec_infer.tar
   ```

2. **解压到对应目录**
   ```bash
   # 检测模型
   tar -xf PP-OCRv5_mobile_det_infer.tar
   mv PP-OCRv5_mobile_det_infer models/PP-OCRv5_mobile_det
   
   # 识别模型
   tar -xf PP-OCRv5_mobile_rec_infer.tar  
   mv PP-OCRv5_mobile_rec_infer models/PP-OCRv5_mobile_rec
   ```

3. **验证文件完整性**
   确保每个模型目录包含以下三个文件：
   - `inference.json`
   - `inference.pdiparams` 
   - `inference.yml`

## 配置说明

### 启用本地模型

在 `config.py` 中的配置：

```python
'local_models': {
    'enabled': True,                    # 启用本地模型
    'models_dir': 'models',            # 模型目录
    'models': {
        'detection': {
            'model_name': 'PP-OCRv5_mobile_det',
            'enabled': True
        },
        'recognition': {
            'model_name': 'PP-OCRv5_mobile_rec', 
            'enabled': True
        }
    }
}
```

### 性能优化配置

针对小图片数字识别的优化配置：

```python
'optimization': {
    'disable_angle_classification': True,    # 禁用角度分类
    'disable_textline_orientation': True,    # 禁用文本行方向
    'disable_doc_orientation': True,         # 禁用文档方向
    'disable_doc_unwarping': True,          # 禁用去扭曲
    'preferred_language': 'en'              # 英文模型对数字更准确
}
```

## 故障排除

### 常见问题

1. **模型文件不存在**
   - 检查文件路径是否正确
   - 确认三个必需文件都已下载

2. **模型加载失败**  
   - 检查文件是否损坏
   - 尝试重新下载模型文件

3. **识别效果不佳**
   - 尝试不同的语言设置（'en' vs 'ch'）
   - 调整预处理优化配置
   - 考虑使用server版本模型

### 回退机制

如果本地模型加载失败，系统会自动：
1. 记录错误日志
2. 回退到在线下载模式
3. 使用优化的PP-OCRv5 Mobile配置

### 调试模式

启用调试模式查看详细日志：
```python
'ocr_settings': {
    'debug_mode': True
}
```

## 版本历史

- **v1.0**: 支持PP-OCRv5 Mobile本地模型
- **v1.1**: 添加小图片数字识别优化配置
- **v1.2**: 增加模型验证和回退机制

## 注意事项

1. **模型版本兼容性**: 确保使用的PaddleOCR版本与模型文件兼容
2. **存储空间**: PP-OCRv5 Mobile模型大约需要10-15MB存储空间
3. **更新机制**: 定期检查模型更新，确保使用最新版本
4. **备份建议**: 建议备份工作正常的模型文件