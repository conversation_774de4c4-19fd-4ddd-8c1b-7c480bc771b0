# -*- coding: utf-8 -*-
"""
股票筛选器主程序
基于指南针软件的股票多空资金数据筛选工具
GUI主入口模块
"""

# 抑制PyTorch警告（在所有其他导入之前）
import os
import warnings
os.environ['PYTORCH_DISABLE_PIN_MEMORY_WARNING'] = '1'
warnings.filterwarnings("ignore", message=".*pin_memory.*")
warnings.filterwarnings("ignore", category=UserWarning, module="torch.*")

import tkinter as tk
import threading
import logging
import sys
from typing import List, Dict, Any, Optional
import queue
import traceback
import keyboard

from config import GUI_CONFIG, APP_CONFIG, COMPASS_SOFTWARE, update_ocr_region_config, update_excel_split_count_config, update_indicator_split_count_config
from data_processor import DataProcessor
from compass_automator import CompassAutomator
from region_selector import RegionSelector
# 尝试导入简化OCR管理器，如果失败则回退到复杂版本
try:
    from simple_ocr_manager import get_global_ocr_manager
except ImportError:
    from ocr_manager_optimized import get_global_ocr_manager

# 导入mixin模块
from gui_setup import GUISetupMixin
from gui_file_operations import GUIFileOperationsMixin  
from gui_ocr_operations import GUIOCROperationsMixin
from gui_mouse_operations import GUIMouseOperationsMixin
from gui_analysis import GUIAnalysisMixin
from gui_display import GUIDisplayMixin
from gui_handlers import GUIHandlersMixin
from gui_buy_signal_operations import GUIBuySignalOperationsMixin
from gui_sell_signal_operations import GUISellSignalOperationsMixin
from gui_stock_import_operations import GUIStockImportOperationsMixin
from gui_web_automation_operations import GUIWebAutomationOperationsMixin
from gui_screenshot_operations import GUIScreenshotOperationsMixin


class StockScreenerGUI(
    GUISetupMixin,
    GUIFileOperationsMixin, 
    GUIOCROperationsMixin,
    GUIMouseOperationsMixin,
    GUIAnalysisMixin,
    GUIDisplayMixin,
    GUIHandlersMixin,
    GUIBuySignalOperationsMixin,
    GUISellSignalOperationsMixin,
    GUIStockImportOperationsMixin,
    GUIWebAutomationOperationsMixin,
    GUIScreenshotOperationsMixin
):
    """股票筛选器GUI主类"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.setup_logging()
        self.setup_window()
        self.setup_variables()
        
        # 消息队列用于线程间通信（需要在setup_widgets之前初始化）
        self.message_queue = queue.Queue()
        
        self.setup_widgets()
        self.setup_layout()
        
        # 初始化组件
        self.data_processor = DataProcessor()
        self.compass_automator = None
        self.ocr_manager = get_global_ocr_manager()
        self.is_processing = False
        self.current_excel_path = ""
        self.selected_region = None  # 存储选择的区域坐标
        self.selected_buy_signal_region = None  # 存储选择的买入信号区域坐标
        self.selected_status_region = None  # 存储选择的状态区域坐标
        
        # 鼠标基准位置相关属性
        self.mouse_target_position = None
        self.mouse_tracking_active = False
        self.mouse_tracking_thread = None
        self.current_mouse_position = (0, 0)
        
        # 暂停分析相关状态变量
        self.is_paused = False
        self.pause_requested = False
        self.current_stock_index = 0
        self.stock_codes_cache = []
        self.should_keep_connection = False  # 控制是否在分析结束时保持指南针连接
        
        self.check_message_queue()
        
        # 日志处理器已在setup_widgets中初始化
        
        # 读取配置文件中的区域信息并初始化OCR
        self.load_saved_region()
        self.load_saved_buy_signal_region()
        self.load_saved_status_region()
        self.load_saved_mouse_position()
        ocr_init_success = self.init_ocr_early()
        
        # 如果OCR初始化成功，提前初始化指南针连接
        if ocr_init_success:
            self.init_compass_early()
        
        # 更新界面状态（在所有组件都初始化完成后）
        self.update_ui_after_init()
        
        # 初始化买入信号组件
        self.init_buy_signal_components()
        
        # 初始化卖出信号组件
        self.init_sell_signal_components()
        
        # 初始化股票自动输入组件
        self.init_stock_import_components()
        
        # 初始化网页自动化组件
        self.init_web_automation_components()
        
        # 启动键盘监听
        self.start_keyboard_listener()
    
    def start_keyboard_listener(self):
        """启动Ctrl+Alt+S快捷键监听线程"""
        def keyboard_listener():
            try:
                # 注册Ctrl+Alt+S组合键监听
                keyboard.add_hotkey('ctrl+alt+s', self.on_pause_hotkey)
                self.logger.info("Ctrl+Alt+S暂停快捷键监听已启动")
                
                # 保持监听线程运行
                keyboard.wait()
            except Exception as e:
                self.logger.error(f"键盘监听线程错误: {str(e)}")
        
        # 启动键盘监听线程
        listener_thread = threading.Thread(target=keyboard_listener, daemon=True)
        listener_thread.start()
        self.logger.info("快捷键监听线程已启动")
    
    def on_pause_hotkey(self):
        """处理Ctrl+Alt+S暂停快捷键"""
        try:
            actions_taken = []
            
            # 1. 处理分析流程暂停（原有逻辑）
            if self.is_processing and not self.is_paused and not self.pause_requested:
                self.pause_requested = True
                actions_taken.append("股票分析")
                self.logger.info("用户按下Ctrl+Alt+S，请求暂停分析")
            
            # 2. 处理买入信号监控停止
            if hasattr(self, 'is_buy_signal_monitoring') and self.is_buy_signal_monitoring:
                if hasattr(self, 'buy_signal_monitor') and self.buy_signal_monitor:
                    self.buy_signal_monitor.stop_monitoring()
                    actions_taken.append("买入信号监控")
                    self.logger.info("用户按下Ctrl+Alt+S，请求停止买入信号监控")
            
            # 3. 处理卖出信号监控停止
            if hasattr(self, 'is_sell_signal_monitoring') and self.is_sell_signal_monitoring:
                if hasattr(self, 'sell_signal_monitor') and self.sell_signal_monitor:
                    self.sell_signal_monitor.stop_monitoring()
                    actions_taken.append("卖出信号监控")
                    self.logger.info("用户按下Ctrl+Alt+S，请求停止卖出信号监控")
            
            # 4. 根据执行的操作生成相应的日志消息
            if actions_taken:
                if len(actions_taken) == 1:
                    self.message_queue.put(("log", f"检测到Ctrl+Alt+S快捷键，{actions_taken[0]}将在当前股票完成后停止..."))
                else:
                    actions_str = "、".join(actions_taken)
                    self.message_queue.put(("log", f"检测到Ctrl+Alt+S快捷键，{actions_str}将在当前股票完成后停止..."))
            else:
                # 没有正在运行的流程
                self.message_queue.put(("log", "检测到Ctrl+Alt+S快捷键，但当前没有正在运行的分析或监控流程"))
                
        except Exception as e:
            self.logger.error(f"处理暂停快捷键事件错误: {str(e)}")
    
    def on_ocr_scale_changed(self, event=None):
        """处理OCR图像缩放模式变化"""
        try:
            # 获取当前选择的值
            selected_text = self.ocr_scale_var.get()
            
            # 映射到配置值
            scale_mode_map = {
                "原图": "original",
                "2倍图": "2x", 
                "4倍图": "4x"
            }
            
            scale_mode = scale_mode_map.get(selected_text, "original")
            
            # 更新配置
            APP_CONFIG['ocr_settings']['image_scale_mode'] = scale_mode
            
            # 记录日志
            self.logger.info(f"OCR图像缩放模式已更改为: {selected_text} ({scale_mode})")
            
        except Exception as e:
            self.logger.error(f"处理OCR缩放模式变化错误: {str(e)}")
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"GUI运行错误: {str(e)}")
            tk.messagebox.showerror("错误", f"程序运行错误: {str(e)}")
        finally:
            if self.compass_automator:
                self.compass_automator.close_compass_software()
            
            # 清理网页自动化资源
            try:
                self.cleanup_web_automation()
            except Exception as e:
                self.logger.error(f"清理网页自动化资源时发生错误: {str(e)}")
    
    def on_split_count_changed(self):
        """Excel拆分数量改变时的回调函数"""
        try:
            split_count = self.excel_split_count_var.get()
            # 验证范围
            if split_count < 2:
                split_count = 2
                self.excel_split_count_var.set(split_count)
            elif split_count > 10:
                split_count = 10
                self.excel_split_count_var.set(split_count)
            
            # 更新配置文件
            success = update_excel_split_count_config(split_count)
            if success:
                self.logger.info(f"Excel拆分数量已更新为: {split_count}")
            else:
                self.logger.error("更新Excel拆分数量配置失败")
                
        except Exception as e:
            self.logger.error(f"处理拆分数量变化时出错: {str(e)}")
    
    def on_indicator_split_count_changed(self):
        """网页指标拆分数量改变时的回调函数"""
        try:
            split_count = self.indicator_split_count_var.get()
            # 验证范围
            if split_count < 2:
                split_count = 2
                self.indicator_split_count_var.set(split_count)
            elif split_count > 10:
                split_count = 10
                self.indicator_split_count_var.set(split_count)
            
            # 更新配置文件
            success = update_indicator_split_count_config(split_count)
            if success:
                self.logger.info(f"网页指标拆分数量已更新为: {split_count}")
            else:
                self.logger.error("更新网页指标拆分数量配置失败")
                
        except Exception as e:
            self.logger.error(f"处理指标拆分数量变化时出错: {str(e)}")


def main():
    """主函数"""
    try:
        app = StockScreenerGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        print(traceback.format_exc())


if __name__ == "__main__":
    main()