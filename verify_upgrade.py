#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证信号分析器升级是否成功
"""

import sys
import traceback

def test_signal_analyzer_upgrade():
    """测试信号分析器是否已升级到增强版本"""
    print("🔍 验证信号分析器升级状态...")
    
    try:
        # 模拟导入（就像GUI系统一样）
        from enhanced_signal_analyzer import SignalAnalyzer
        from config import APP_CONFIG
        
        print("✅ 增强版SignalAnalyzer导入成功")
        
        # 检查是否是增强版本
        if hasattr(SignalAnalyzer, '__name__') and 'Enhanced' in str(SignalAnalyzer):
            print("✅ 确认使用的是EnhancedSignalAnalyzer")
        else:
            print("✅ SignalAnalyzer别名设置正确")
        
        # 尝试创建实例（模拟信号区域）
        signal_region = {'x': 100, 'y': 100, 'width': 200, 'height': 50}
        
        # 创建一个模拟的OCR管理器
        class MockOCRManager:
            def is_initialized(self):
                return True
            def get_ocr_engine(self):
                return None
        
        analyzer = SignalAnalyzer(MockOCRManager(), signal_region, APP_CONFIG)
        print("✅ 增强版SignalAnalyzer实例创建成功")
        
        # 检查关键属性
        if hasattr(analyzer, 'recognition_mode'):
            print(f"✅ 识别模式: {analyzer.recognition_mode}")
        if hasattr(analyzer, 'color_enabled'):
            print(f"✅ 颜色识别: {'启用' if analyzer.color_enabled else '禁用'}")
        if hasattr(analyzer, 'color_ranges'):
            print(f"✅ 颜色范围配置: {len(analyzer.color_ranges)} 种信号类型")
        
        print("\n🎉 信号分析器升级验证成功！")
        print("📈 系统现在支持：")
        print("   • 颜色识别优先（超快速度）")
        print("   • OCR智能回退（保证准确性）")
        print("   • 混合模式自动切换")
        print("   • 详细性能统计")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("   请检查enhanced_signal_analyzer.py文件是否存在")
        return False
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_gui_integration():
    """测试GUI集成是否正确"""
    print("\n🔗 验证GUI集成状态...")
    
    try:
        # 模拟GUI导入
        from gui_signal_operations import GUISignalOperationsMixin
        print("✅ GUI信号操作模块导入成功")
        
        # 检查是否正确导入了增强版本
        import gui_signal_operations
        import inspect
        
        source = inspect.getsource(gui_signal_operations)
        if 'from enhanced_signal_analyzer import SignalAnalyzer' in source:
            print("✅ GUI模块使用增强版SignalAnalyzer")
        else:
            print("❌ GUI模块仍使用原始版SignalAnalyzer")
            return False
            
        if 'APP_CONFIG' in source:
            print("✅ GUI模块正确传递配置参数")
        else:
            print("❌ GUI模块未传递配置参数")
            return False
        
        print("✅ GUI集成验证成功")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成验证失败: {str(e)}")
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 信号分析器升级验证")
    print("=" * 60)
    
    success1 = test_signal_analyzer_upgrade()
    success2 = test_gui_integration()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有验证通过！系统已成功升级到颜色识别+OCR混合模式")
        print("💡 现在可以启动买卖信号监控，享受超快识别速度！")
    else:
        print("⚠️ 验证发现问题，请检查上述错误信息")
    print("=" * 60)