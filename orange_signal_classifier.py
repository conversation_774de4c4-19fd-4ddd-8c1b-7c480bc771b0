#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
橙色信号二级分类器
专门用于区分开仓和持仓信号的二级识别策略
"""

import cv2
import numpy as np
import logging
import time
from typing import Tuple, Optional, Dict, Any
from PIL import Image
import os
import tempfile

class OrangeSignalClassifier:
    """橙色信号二级分类器"""
    
    def __init__(self, ocr_manager=None):
        """初始化分类器"""
        self.logger = logging.getLogger(__name__)
        self.ocr_manager = ocr_manager
        
        # 橙色信号检测配置（精确检测橙色，避免误判绿色）
        self.orange_detection_config = {
            # HSV橙色范围（精确的橙色检测，排除绿色）
            'hsv_ranges': [
                {'lower': np.array([10, 150, 80]), 'upper': np.array([25, 255, 255])},   # 橙色主范围
                {'lower': np.array([0, 120, 60]), 'upper': np.array([15, 255, 255])}     # 红橙色范围
            ],
            # RGB橙色范围（基于字体颜色分析）
            'rgb_ranges': [
                {'center': [138, 82, 3], 'tolerance': 50},   # 持仓信号中心（橙色）
                {'center': [106, 47, 52], 'tolerance': 50}   # 开仓信号中心（红橙色）
            ],
            'min_orange_pixels': 15,  # 最小橙色像素数
            'orange_ratio_threshold': 0.08  # 橙色像素比例阈值
        }
        
        # OCR关键字配置
        self.ocr_keywords = {
            '持仓': ['持仓', '持', '仓', 'CC', 'cc'],
            '开仓': ['开仓', '开', '仓', 'KC', 'kc', 'KG', 'kg']
        }
        
        # 统计信息
        self.stats = {
            'total_classifications': 0,
            'orange_detected': 0,
            'ocr_success': 0,
            'ocr_failed': 0,
            'classification_success': 0
        }
    
    def is_orange_signal(self, image: np.ndarray) -> Tuple[bool, Dict]:
        """
        检测图像是否包含橙色信号
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            (is_orange, details)
        """
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 过滤背景
            bg_mask = hsv[:,:,2] > 30  # 去除太暗的像素
            
            # 检测橙色像素
            orange_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            
            for hsv_range in self.orange_detection_config['hsv_ranges']:
                mask = cv2.inRange(hsv, hsv_range['lower'], hsv_range['upper'])
                mask = mask & bg_mask.astype(np.uint8) * 255
                orange_mask |= mask
            
            orange_pixel_count = cv2.countNonZero(orange_mask)
            total_pixels = image.shape[0] * image.shape[1]
            orange_ratio = orange_pixel_count / total_pixels
            
            is_orange = (orange_pixel_count >= self.orange_detection_config['min_orange_pixels'] and 
                        orange_ratio >= self.orange_detection_config['orange_ratio_threshold'])
            
            details = {
                'orange_pixel_count': orange_pixel_count,
                'total_pixels': total_pixels,
                'orange_ratio': orange_ratio,
                'min_pixels_threshold': self.orange_detection_config['min_orange_pixels'],
                'ratio_threshold': self.orange_detection_config['orange_ratio_threshold']
            }
            
            return is_orange, details
            
        except Exception as e:
            self.logger.error(f"橙色信号检测失败: {e}")
            return False, {'error': str(e)}
    
    def classify_orange_signal_by_ocr(self, image: np.ndarray) -> Tuple[Optional[str], float, Dict]:
        """
        使用OCR对橙色信号进行二级分类
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            (signal_type, confidence, details)
        """
        if not self.ocr_manager:
            return None, 0.0, {'error': 'OCR管理器未初始化'}
        
        try:
            # 转换为PIL图像
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            
            # 执行OCR识别
            ocr_text = self._perform_ocr_recognition(pil_image)
            
            if not ocr_text:
                return None, 0.0, {'error': 'OCR识别无结果', 'ocr_text': ''}
            
            # 清理OCR文本
            cleaned_text = self._clean_ocr_text(ocr_text)
            
            # 关键字匹配
            signal_type, confidence = self._match_keywords(cleaned_text)
            
            details = {
                'ocr_text': ocr_text,
                'cleaned_text': cleaned_text,
                'matched_keywords': self._get_matched_keywords(cleaned_text)
            }
            
            return signal_type, confidence, details
            
        except Exception as e:
            self.logger.error(f"OCR分类失败: {e}")
            return None, 0.0, {'error': str(e)}
    
    def _perform_ocr_recognition(self, pil_image: Image.Image) -> str:
        """执行OCR识别"""
        try:
            # 获取OCR引擎
            ocr_engine = self.ocr_manager.get_ocr_engine()
            if not ocr_engine:
                self.logger.error("无法获取OCR引擎")
                return ""

            # 尝试不同的OCR接口
            if hasattr(ocr_engine, 'simple_engine'):
                # 简化PaddleOCR引擎 - 转换PIL图像为numpy数组
                try:
                    import numpy as np
                    image_array = np.array(pil_image)
                    # 如果是RGB，转换为BGR（OpenCV格式）
                    if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                        image_array = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)

                    results = ocr_engine.simple_engine.recognize_image(image_array)
                    if isinstance(results, list) and results:
                        return ' '.join(results)
                    elif isinstance(results, str):
                        return results
                except Exception as e:
                    self.logger.error(f"简化PaddleOCR识别失败: {e}")

            # 保存临时图片文件用于其他OCR接口
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name
                pil_image.save(temp_path)

            try:
                if hasattr(ocr_engine, 'ocr'):
                    # PaddleOCR接口
                    results = ocr_engine.ocr(temp_path, cls=True)
                    if results and len(results) > 0 and results[0]:
                        combined_text = ''.join([item[1][0] for item in results[0] if len(item) > 1])
                        return combined_text
                elif hasattr(ocr_engine, 'recognize_from_image'):
                    # 其他OCR接口
                    results = ocr_engine.recognize_from_image(pil_image)
                    if isinstance(results, dict) and 'text' in results:
                        return results['text']
                    elif isinstance(results, str):
                        return results

                return ""

            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)

        except Exception as e:
            self.logger.error(f"OCR识别执行失败: {e}")
            return ""
    
    def _clean_ocr_text(self, text: str) -> str:
        """清理OCR识别的文本"""
        if not text:
            return ""
        
        # 移除空白字符
        cleaned = text.strip()
        
        # 移除常见的OCR错误字符
        cleaned = cleaned.replace(' ', '').replace('\n', '').replace('\t', '')
        
        # 移除标点符号
        import re
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)
        
        return cleaned
    
    def _match_keywords(self, text: str) -> Tuple[Optional[str], float]:
        """关键字匹配"""
        if not text:
            return None, 0.0
        
        text_lower = text.lower()
        best_match = None
        best_score = 0.0
        
        for signal_type, keywords in self.ocr_keywords.items():
            for keyword in keywords:
                keyword_lower = keyword.lower()
                
                # 完全匹配
                if keyword_lower == text_lower:
                    return signal_type, 1.0
                
                # 包含匹配
                if keyword_lower in text_lower:
                    score = len(keyword_lower) / len(text_lower)
                    if score > best_score:
                        best_match = signal_type
                        best_score = score
                
                # 反向包含匹配
                if text_lower in keyword_lower:
                    score = len(text_lower) / len(keyword_lower)
                    if score > best_score:
                        best_match = signal_type
                        best_score = score
        
        return best_match, best_score
    
    def _get_matched_keywords(self, text: str) -> Dict[str, list]:
        """获取匹配的关键字详情"""
        matched = {}
        text_lower = text.lower()
        
        for signal_type, keywords in self.ocr_keywords.items():
            matched_keywords = []
            for keyword in keywords:
                if keyword.lower() in text_lower or text_lower in keyword.lower():
                    matched_keywords.append(keyword)
            if matched_keywords:
                matched[signal_type] = matched_keywords
        
        return matched
    
    def classify_orange_signal(self, image: np.ndarray) -> Dict[str, Any]:
        """
        橙色信号完整分类流程
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            分类结果字典
        """
        start_time = time.time()
        self.stats['total_classifications'] += 1
        
        result = {
            'success': False,
            'signal': None,
            'confidence': 0.0,
            'method': 'orange_classification',
            'processing_time': 0.0,
            'details': {}
        }
        
        try:
            # 1. 检测是否为橙色信号
            is_orange, orange_details = self.is_orange_signal(image)
            result['details']['orange_detection'] = orange_details
            
            if not is_orange:
                result['details']['reason'] = 'not_orange_signal'
                return result
            
            self.stats['orange_detected'] += 1
            
            # 2. 使用OCR进行二级分类
            signal_type, confidence, ocr_details = self.classify_orange_signal_by_ocr(image)
            result['details']['ocr_classification'] = ocr_details
            
            if signal_type and confidence >= 0.3:
                result.update({
                    'success': True,
                    'signal': signal_type,
                    'confidence': confidence
                })
                self.stats['classification_success'] += 1
                self.stats['ocr_success'] += 1
            else:
                result['details']['reason'] = 'ocr_classification_failed'
                self.stats['ocr_failed'] += 1
            
        except Exception as e:
            self.logger.error(f"橙色信号分类失败: {e}")
            result['details']['error'] = str(e)
        
        finally:
            result['processing_time'] = time.time() - start_time
        
        return result
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        total = self.stats['total_classifications']
        if total == 0:
            return self.stats
        
        return {
            **self.stats,
            'orange_detection_rate': self.stats['orange_detected'] / total,
            'ocr_success_rate': self.stats['ocr_success'] / max(1, self.stats['orange_detected']),
            'overall_success_rate': self.stats['classification_success'] / total
        }

def test_orange_classifier():
    """测试橙色信号分类器"""
    from simple_ocr_manager import get_simple_ocr_manager
    
    # 初始化OCR管理器
    ocr_manager = get_simple_ocr_manager()
    if not ocr_manager.initialize_ocr():
        print("❌ OCR管理器初始化失败")
        return
    
    # 创建分类器
    classifier = OrangeSignalClassifier(ocr_manager)
    
    # 测试图片
    test_images = [
        ('screenshots/cc2.png', '持仓'),  # 持仓信号
        ('screenshots/kc2.png', '开仓')   # 开仓信号
    ]
    
    print("=== 橙色信号二级分类测试 ===\n")
    
    for image_path, expected in test_images:
        if not os.path.exists(image_path):
            print(f"❌ 图片不存在: {image_path}")
            continue
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图片: {image_path}")
            continue
        
        print(f"测试图片: {image_path} (期望: {expected})")
        
        # 执行分类
        result = classifier.classify_orange_signal(image)
        
        if result['success']:
            print(f"  ✅ 分类成功: {result['signal']}")
            print(f"  置信度: {result['confidence']:.3f}")
            print(f"  处理时间: {result['processing_time']:.3f}s")
            
            # 显示OCR详情
            ocr_details = result['details'].get('ocr_classification', {})
            if 'cleaned_text' in ocr_details:
                print(f"  OCR文本: '{ocr_details['cleaned_text']}'")
        else:
            print(f"  ❌ 分类失败: {result['details'].get('reason', 'unknown')}")
            if 'error' in result['details']:
                print(f"  错误: {result['details']['error']}")
        print()
    
    # 显示统计信息
    stats = classifier.get_statistics()
    print("=== 分类统计 ===")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.3f}")
        else:
            print(f"{key}: {value}")

if __name__ == "__main__":
    test_orange_classifier()
