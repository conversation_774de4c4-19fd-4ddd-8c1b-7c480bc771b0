# -*- coding: utf-8 -*-
"""
指南针软件硬件相关配置文件
包含与PC硬件相关的配置参数，如OCR区域坐标、鼠标位置等
这些配置与PC环境密切相关，独立管理以避免程序更新时被覆盖
"""

import os
import re
import json
from typing import Dict, Any, Optional


# PC硬件相关配置
HARDWARE_CONFIG = {
    # OCR截图区域坐标（绝对屏幕坐标）
    # 这个区域应该包含资金流数据显示区域
    'ocr_region': {
        'x': 453,
        'y': 1109,
        'width': 122,
        'height': 93
    },
    
    # 买入信号OCR区域坐标（绝对屏幕坐标）
    # 这个区域应该包含买入信号显示区域
    'buy_signal_region': {
        'x': 333,
        'y': 139,
        'width': 59,
        'height': 45
    },
    
    # 状态区域OCR坐标（绝对屏幕坐标）
    # 这个区域应该包含状态信息显示区域
    'status_region': {
        'x': 752,
        'y': 142,
        'width': 277,
        'height': 33
    },
    
    # 鼠标基准位置坐标（绝对屏幕坐标）
    # 用于在每只股票分析前重置鼠标位置，确保操作准确性
    'mouse_target_position': {
        'x': 2105,
        'y': 521
    }
}


def get_hardware_config(key: Optional[str] = None) -> Any:
    """
    获取硬件配置
    
    Args:
        key: 配置键名，如果为None则返回全部配置
        
    Returns:
        配置值或全部配置字典
    """
    if key is None:
        return HARDWARE_CONFIG.copy()
    return HARDWARE_CONFIG.get(key)


def update_hardware_config(key: str, value: Any) -> bool:
    """
    更新硬件配置并保存到文件
    
    Args:
        key: 配置键名
        value: 配置值
        
    Returns:
        bool: 是否更新成功
    """
    try:
        # 更新内存中的配置
        HARDWARE_CONFIG[key] = value
        
        # 保存到文件
        return _save_hardware_config_to_file(key, value)
        
    except Exception as e:
        print(f"更新硬件配置失败: {str(e)}")
        return False


def _save_hardware_config_to_file(key: str, value: Any) -> bool:
    """
    将硬件配置保存到文件
    
    Args:
        key: 配置键名
        value: 配置值
        
    Returns:
        bool: 是否保存成功
    """
    try:
        # 获取当前文件路径
        config_file = os.path.join(os.path.dirname(__file__), 'compass_hardware_config.py')
        
        # 读取当前文件内容
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 构造新的配置值字符串
        if isinstance(value, dict):
            # 处理字典类型的配置
            value_lines = []
            for k, v in value.items():
                value_lines.append(f"        '{k}': {v}")
            value_str = "{\n" + ",\n".join(value_lines) + "\n    }"
        else:
            value_str = str(value)
        
        # 使用正则表达式替换对应的配置项
        pattern = r"('" + key + r"':\s*)\{[^}]*\}"
        replacement = f"'{key}': {value_str}"
        
        new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # 如果没有找到匹配项，可能是简单值类型
        if new_content == content:
            pattern = r"('" + key + r"':\s*)[^,\n}]*"
            replacement = f"'{key}': {value_str}"
            new_content = re.sub(pattern, replacement, content)
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        return True
        
    except Exception as e:
        print(f"保存硬件配置到文件失败: {str(e)}")
        return False


def update_ocr_region(x: int, y: int, width: int, height: int) -> bool:
    """
    更新OCR区域配置
    
    Args:
        x: 区域左上角X坐标（绝对屏幕坐标）
        y: 区域左上角Y坐标（绝对屏幕坐标）
        width: 区域宽度
        height: 区域高度
        
    Returns:
        bool: 是否更新成功
    """
    region_config = {
        'x': x,
        'y': y,
        'width': width,
        'height': height
    }
    return update_hardware_config('ocr_region', region_config)


def update_buy_signal_region(x: int, y: int, width: int, height: int) -> bool:
    """
    更新买入信号区域配置
    
    Args:
        x: 区域左上角X坐标（绝对屏幕坐标）
        y: 区域左上角Y坐标（绝对屏幕坐标）
        width: 区域宽度
        height: 区域高度
        
    Returns:
        bool: 是否更新成功
    """
    region_config = {
        'x': x,
        'y': y,
        'width': width,
        'height': height
    }
    return update_hardware_config('buy_signal_region', region_config)


def update_status_region(x: int, y: int, width: int, height: int) -> bool:
    """
    更新状态区域配置
    
    Args:
        x: 区域左上角X坐标（绝对屏幕坐标）
        y: 区域左上角Y坐标（绝对屏幕坐标）
        width: 区域宽度
        height: 区域高度
        
    Returns:
        bool: 是否更新成功
    """
    region_config = {
        'x': x,
        'y': y,
        'width': width,
        'height': height
    }
    return update_hardware_config('status_region', region_config)


def update_mouse_target_position(x: int, y: int) -> bool:
    """
    更新鼠标基准位置配置
    
    Args:
        x: 鼠标基准位置X坐标（绝对屏幕坐标）
        y: 鼠标基准位置Y坐标（绝对屏幕坐标）
        
    Returns:
        bool: 是否更新成功
    """
    position_config = {
        'x': x,
        'y': y
    }
    return update_hardware_config('mouse_target_position', position_config)


# 提供简化的访问接口，与原有代码兼容
def get_ocr_region() -> Dict[str, int]:
    """获取OCR区域配置"""
    return get_hardware_config('ocr_region') or {}


def get_buy_signal_region() -> Dict[str, int]:
    """获取买入信号区域配置"""
    return get_hardware_config('buy_signal_region') or {}


def get_status_region() -> Dict[str, int]:
    """获取状态区域配置"""
    return get_hardware_config('status_region') or {}


def get_mouse_target_position() -> Dict[str, int]:
    """获取鼠标基准位置配置"""
    return get_hardware_config('mouse_target_position') or {}


def backup_hardware_config() -> bool:
    """
    备份当前硬件配置到JSON文件
    
    Returns:
        bool: 是否备份成功
    """
    try:
        backup_file = os.path.join(os.path.dirname(__file__), 'compass_hardware_config_backup.json')
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(HARDWARE_CONFIG, f, ensure_ascii=False, indent=4)
        
        print(f"硬件配置已备份到: {backup_file}")
        return True
        
    except Exception as e:
        print(f"备份硬件配置失败: {str(e)}")
        return False


def restore_hardware_config_from_backup() -> bool:
    """
    从备份文件恢复硬件配置
    
    Returns:
        bool: 是否恢复成功
    """
    try:
        backup_file = os.path.join(os.path.dirname(__file__), 'compass_hardware_config_backup.json')
        
        if not os.path.exists(backup_file):
            print("备份文件不存在")
            return False
        
        with open(backup_file, 'r', encoding='utf-8') as f:
            backup_config = json.load(f)
        
        # 逐个更新配置项
        for key, value in backup_config.items():
            if not update_hardware_config(key, value):
                print(f"恢复配置项 {key} 失败")
                return False
        
        print("硬件配置已从备份恢复")
        return True
        
    except Exception as e:
        print(f"从备份恢复硬件配置失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 测试代码
    print("硬件配置管理器测试")
    print("当前配置:")
    import pprint
    pprint.pprint(get_hardware_config())
    
    # 备份配置
    backup_hardware_config()