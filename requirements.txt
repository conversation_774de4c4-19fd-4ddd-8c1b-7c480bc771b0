# 股票筛选器依赖包
# GUI framework - tkinter is included with Python standard library

# OCR处理和图像处理
paddleocr==3.1.0
paddlepaddle==3.1.0
paddlex==3.1.3
opencv-python==4.12.0.88
opencv-contrib-python==4.10.0.84
Pillow==11.1.0
numpy==2.2.4
mss==10.0.0

# Windows自动化
pywinauto==0.6.9
psutil==7.0.0
pyautogui==0.9.54
keyboard==0.13.5
comtypes==1.4.11
pywin32==311

# Web自动化
playwright==1.54.0
beautifulsoup4==4.13.3
lxml==5.3.1
cssselect==1.3.0
cssutils==2.11.1
premailer==3.10.0

# 数据处理
pandas==2.2.3
openpyxl==3.1.5
xlrd==2.0.1
et_xmlfile==2.0.0

# 网络通信
requests==2.32.3
requests-toolbelt==1.0.0
urllib3==2.2.3
httpx==0.25.2
httpcore==1.0.7
aiohttp==3.12.14
aiosignal==1.4.0
anyio==3.7.1
h11==0.14.0

# AI/机器学习框架
torch==2.7.1
torchvision==0.22.1
langchain==0.3.26
langchain-core==0.3.69
langchain-community==0.3.27
langchain-openai==0.3.28
langchain-text-splitters==0.3.8
langsmith==0.4.8
openai==1.97.0
tiktoken==0.9.0
tokenizers==0.21.2
huggingface-hub==0.33.4

# 工具库
attrs==25.1.0
certifi==2025.1.31
charset-normalizer==3.4.0
click==8.2.1
colorama==0.4.6
colorlog==6.9.0
dataclasses-json==0.6.7
decorator==5.2.1
distro==1.9.0
einops==0.8.1
filelock==3.18.0
frozenlist==1.7.0
fsspec==2025.7.0
ftfy==6.3.1
GPUtil==1.4.0
greenlet==3.2.3
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
more-itertools==10.7.0
MouseInfo==0.1.3
mpmath==1.3.0
multidict==6.6.3
mypy_extensions==1.1.0
networkx==3.5
orjson==3.11.0
packaging==24.2
prettytable==3.16.0
propcache==0.3.2
protobuf==5.29.3
py-cpuinfo==9.0.0
pyclipper==1.3.0.post6
pycryptodome==3.23.0
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
pyee==13.0.0
PyGetWindow==0.0.9
PyMsgBox==1.0.9
pypdfium2==4.30.1
pyperclip==1.9.0
PyRect==0.2.0
PyScreeze==1.0.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
pytweening==1.2.0
pytz==2024.2
PyYAML==6.0.2
regex==2024.11.6
ruamel.yaml==0.18.14
ruamel.yaml.clib==0.2.12
scikit-learn==1.7.1
scipy==1.16.0
setuptools==75.8.0
shapely==2.1.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.41
sympy==1.14.0
tenacity==8.2.3
threadpoolctl==3.6.0
tqdm==4.67.1
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.12.2
tzdata==2024.2
ujson==5.10.0
wcwidth==0.2.13
zstandard==0.23.0