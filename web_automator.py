# -*- coding: utf-8 -*-
"""
网页自动化模块
基于Playwright的topxlc.com小草选股数据抓取
"""

import asyncio
import logging
import os
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON>er, BrowserContext

from config import WEB_AUTOMATION_CONFIG
from optimized_virtual_scroll import OptimizedVirtualScrollHandler
from html_data_parser import create_html_data_parser_manager


class WebAutomator:
    """网页自动化操作类"""
    
    def __init__(self):
        """初始化网页自动化操作器"""
        self.config = WEB_AUTOMATION_CONFIG
        self.logger = logging.getLogger(__name__)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.is_logged_in = False
        self.is_ready_for_extraction = False
        
    async def initialize_browser(self):
        """初始化浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=self.config['browser_headless'],
                timeout=self.config['browser_timeout']
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {str(e)}")
            return False
    
    async def navigate_to_site(self):
        """导航到目标网站"""
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            # 导航到目标网站
            await self.page.goto(self.config['target_url'])
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(self.config['page_load_wait'])
            
            self.logger.info(f"成功导航到: {self.config['target_url']}")
            return True
            
        except Exception as e:
            self.logger.error(f"导航到网站失败: {str(e)}")
            return False
    
    async def auto_login(self, username: str, password: str):
        """自动登录功能"""
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            # 加载选择器配置
            from web_element_selector import get_web_element_selector_manager
            selector_manager = get_web_element_selector_manager()
            selectors = selector_manager.load_selectors_from_config()
            
            if not selectors:
                self.logger.warning("未找到选择器配置，跳过自动登录")
                return True  # 返回True让用户手动登录
            
            self.logger.info("开始自动登录...")
            
            # 等待并填写用户名
            if 'username_input' in selectors:
                username_selector = selectors['username_input']
                self.logger.info(f"查找用户名输入框: {username_selector}")
                
                await self.page.wait_for_selector(username_selector, timeout=10000)
                await self.page.fill(username_selector, username)
                self.logger.info("用户名已填写")
                
                # 等待一下避免操作过快
                await asyncio.sleep(0.5)
            
            # 等待并填写密码
            if 'password_input' in selectors:
                password_selector = selectors['password_input']
                self.logger.info(f"查找密码输入框: {password_selector}")
                
                await self.page.wait_for_selector(password_selector, timeout=10000)
                await self.page.fill(password_selector, password)
                self.logger.info("密码已填写")
                
                # 等待一下避免操作过快
                await asyncio.sleep(0.5)
            
            # 用户名和密码填写完成，开始自动登录
            self.logger.info("✅ 用户名和密码填写完成，开始自动登录...")
            
            # 检查是否启用自动登录
            auto_login_config = self.config.get('auto_login', {})
            if not auto_login_config.get('enabled', True):
                self.logger.info("⚠️ 自动登录已禁用，需要用户手动登录")
                return True
            
            login_success = False
            login_method = auto_login_config.get('login_method', 'auto')
            login_button_timeout = auto_login_config.get('login_button_timeout', 5000)
            login_wait_time = auto_login_config.get('login_wait_time', 2)
            
            # 根据配置选择登录方式
            if login_method in ['button', 'auto']:
                # 尝试方法1：点击登录按钮（优先）
                if 'login_button' in selectors:
                    login_button_selector = selectors['login_button']
                    self.logger.info(f"尝试点击登录按钮: {login_button_selector}")
                    
                    try:
                        # 等待登录按钮出现
                        await self.page.wait_for_selector(login_button_selector, timeout=login_button_timeout)
                        await self.page.click(login_button_selector)
                        self.logger.info("✅ 已点击登录按钮")
                        login_success = True
                        
                    except Exception as e:
                        self.logger.warning(f"点击登录按钮失败: {str(e)}")
                        if login_method == 'button':
                            # 如果指定了button方式但失败了，不再尝试其他方式
                            login_success = False
            
            # 如果是enter方式或auto方式且按钮失败，则尝试回车键
            if (login_method in ['enter', 'auto'] and not login_success):
                self.logger.info("尝试使用回车键登录...")
                try:
                    # 确保密码输入框处于焦点状态
                    if 'password_input' in selectors:
                        await self.page.focus(selectors['password_input'])
                    
                    # 按回车键登录
                    await self.page.keyboard.press('Enter')
                    self.logger.info("✅ 已按回车键尝试登录")
                    login_success = True
                    
                except Exception as e:
                    self.logger.warning(f"回车键登录失败: {str(e)}")
            
            if login_success:
                # 等待登录处理
                self.logger.info(f"等待登录处理（{login_wait_time}秒）...")
                await asyncio.sleep(login_wait_time)
                
                # 检测登录结果（如果启用）
                if auto_login_config.get('enable_login_detection', True):
                    await self._detect_login_result()
                
            else:
                self.logger.warning("⚠️ 自动登录尝试失败，需要用户手动登录")
            
            self.is_logged_in = True  # 标记为已处理登录流程
            return True
                
        except Exception as e:
            self.logger.error(f"自动登录失败: {str(e)}")
            # 自动登录失败不算致命错误，返回True让用户手动登录
            return True
    
    async def _detect_login_result(self):
        """检测登录结果"""
        try:
            self.logger.info("检测登录结果...")
            
            # 等待页面响应
            await asyncio.sleep(1)
            
            # 方法1：检查URL变化（登录成功通常会重定向）
            current_url = self.page.url
            self.logger.info(f"当前页面URL: {current_url}")
            
            # 方法2：检查页面内容变化
            # 查找常见的登录失败提示
            login_error_selectors = [
                ".error-message", ".login-error", ".alert-danger", 
                ".error", "[class*='error']", "[class*='fail']",
                "div:has-text('用户名或密码错误')", "div:has-text('登录失败')",
                "div:has-text('账号或密码不正确')", "div:has-text('验证失败')"
            ]
            
            has_error = False
            for selector in login_error_selectors:
                try:
                    error_element = await self.page.query_selector(selector)
                    if error_element:
                        error_text = await error_element.inner_text()
                        if error_text and error_text.strip():
                            self.logger.error(f"❌ 检测到登录错误: {error_text}")
                            has_error = True
                            break
                except:
                    continue
            
            # 方法3：检查是否还在登录页面（查找登录表单）
            still_on_login_page = False
            login_form_selectors = [
                "form[class*='login']", "div[class*='login']", 
                "input[type='password']", "button:has-text('登录')",
                "button:has-text('Login')", "input[placeholder*='密码']"
            ]
            
            for selector in login_form_selectors:
                try:
                    form_element = await self.page.query_selector(selector)
                    if form_element and await form_element.is_visible():
                        still_on_login_page = True
                        break
                except:
                    continue
            
            # 判断登录结果
            if has_error:
                self.logger.error("❌ 登录失败：检测到错误信息")
            elif still_on_login_page:
                self.logger.warning("⚠️ 可能仍在登录页面，登录可能未成功")
            else:
                self.logger.info("✅ 登录可能成功：未检测到错误，且页面已变化")
            
        except Exception as e:
            self.logger.warning(f"登录结果检测失败: {str(e)}")
    
    def wait_for_user_navigation(self):
        """等待用户手动导航到指定页面"""
        """
        这个函数在GUI中被调用，提示用户手动导航到数据页面
        然后用户点击"开始抓取"按钮继续
        """
        self.logger.info("等待用户手动导航到数据页面...")
        return True
    
    def set_ready_for_extraction(self):
        """设置准备开始数据抓取状态"""
        self.is_ready_for_extraction = True
        self.logger.info("已设置为准备抓取数据状态")
    
    async def _scroll_and_load_all_data(self, progress_callback=None):
        """
        优化版滚动加载所有数据（使用OptimizedVirtualScrollHandler）
        
        Args:
            progress_callback: 进度回调函数，接收 (scroll_count, current_rows, message) 参数
            
        Returns:
            tuple: (success, message, final_row_count)
        """
        try:
            # 使用优化的虚拟滚动处理器，并保存引用以便后续获取收集的数据
            self.scroll_handler = OptimizedVirtualScrollHandler(self.page, self.logger)
            return await self.scroll_handler.scroll_and_load_all_data(progress_callback)
            
        except Exception as e:
            error_msg = f"优化滚动加载过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, 0
    async def _detect_scroll_container(self):
        """
        检测页面中的滚动容器
        
        Returns:
            dict: 包含滚动容器信息的字典
        """
        try:
            # 尝试多种策略检测滚动容器
            detect_script = """
            () => {
                // 策略1: 查找常见的滚动容器选择器
                const commonSelectors = [
                    '.table-container',
                    '.scroll-container', 
                    '.data-container',
                    '[class*="scroll"]',
                    '[class*="table"]',
                    '[class*="container"]',
                    '.ant-table-body',
                    '.el-table__body-wrapper',
                    '.c-table'
                ];
                
                for (const selector of commonSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        if (element.scrollHeight > element.clientHeight) {
                            return {
                                found: true,
                                selector: selector,
                                script: `document.querySelector('${selector}')`,
                                description: `容器选择器: ${selector}`
                            };
                        }
                    }
                }
                
                // 策略2: 查找包含表格数据的父容器
                const tableRows = document.querySelectorAll('.c-table-row-body');
                if (tableRows.length > 0) {
                    let parent = tableRows[0].parentElement;
                    while (parent && parent !== document.body) {
                        if (parent.scrollHeight > parent.clientHeight) {
                            const classList = Array.from(parent.classList).join('.');
                            return {
                                found: true,
                                selector: '.' + classList,
                                script: `document.querySelector('.${classList}')`,
                                description: `表格父容器: .${classList}`
                            };
                        }
                        parent = parent.parentElement;
                    }
                }
                
                // 策略3: 查找所有可滚动的元素
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {
                    const style = window.getComputedStyle(element);
                    if ((style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') 
                        && element.scrollHeight > element.clientHeight) {
                        const classList = Array.from(element.classList).join('.');
                        if (classList) {
                            return {
                                found: true,
                                selector: '.' + classList,
                                script: `document.querySelector('.${classList}')`,
                                description: `可滚动元素: .${classList}`
                            };
                        }
                    }
                }
                
                // 没有找到合适的滚动容器
                return {
                    found: false,
                    selector: null,
                    script: 'window',
                    description: '未找到滚动容器，使用window'
                };
            }
            """
            
            result = await self.page.evaluate(detect_script)
            self.logger.info(f"滚动容器检测结果: {result['description']}")
            return result
            
        except Exception as e:
            self.logger.error(f"检测滚动容器失败: {e}")
            return {
                'found': False,
                'selector': None,
                'script': 'window',
                'description': f'检测失败，使用window: {str(e)}'
            }
    
    async def extract_stock_data(self, progress_callback=None):
        """
        抓取股票数据（包含滚动加载）
        
        Args:
            progress_callback: 进度回调函数，用于GUI更新
        """
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            if not self.is_ready_for_extraction:
                raise Exception("尚未准备好开始数据抓取")
            
            # 等待页面加载完成
            try:
                await self.page.wait_for_load_state('networkidle', timeout=300000)
            except Exception as e:
                self.logger.warning(f"等待页面加载超时: {e}, 继续尝试获取数据")
            
            self.logger.info("开始数据抓取流程...")
            
            # 步骤1: 滚动加载所有数据
            self.logger.info("步骤1: 执行滚动加载，获取所有数据...")
            if progress_callback:
                progress_callback(0, 0, "开始滚动加载数据...")
            
            scroll_success, scroll_message, final_row_count = await self._scroll_and_load_all_data(progress_callback)
            
            if not scroll_success:
                raise Exception(f"滚动加载失败: {scroll_message}")
            
            self.logger.info(f"滚动加载完成: {scroll_message}")
            if progress_callback:
                progress_callback(0, final_row_count, f"滚动加载完成，共{final_row_count}条记录")
            
            # 步骤2: 检查是否有滚动过程中收集的数据
            self.logger.info("步骤2: 检查滚动收集的数据...")
            if progress_callback:
                progress_callback(0, final_row_count, "正在检查滚动收集的数据...")
            
            # 尝试从虚拟滚动器获取已收集的数据
            scroll_handler = getattr(self, 'scroll_handler', None)
            collected_data = []
            
            if scroll_handler and hasattr(scroll_handler, 'get_collected_data'):
                collected_data = scroll_handler.get_collected_data()
                self.logger.info(f"从滚动过程中获取到 {len(collected_data)} 条预收集数据")
            
            # 如果没有预收集数据或数据量不足，则使用传统HTML解析方式
            if len(collected_data) < 10:  # 如果收集的数据太少，可能滚动收集有问题
                self.logger.info("预收集数据不足，使用传统HTML解析方式...")
                
                # 步骤2A: 获取页面HTML内容
                self.logger.info("步骤2A: 获取页面HTML内容...")
                if progress_callback:
                    progress_callback(0, final_row_count, "正在获取页面HTML内容...")
                
                try:
                    html_content = await self.page.content()
                    if not html_content or len(html_content.strip()) < 100:
                        raise Exception("获取到的HTML内容为空或过短，请确保页面已正常加载")
                except Exception as e:
                    raise Exception(f"获取页面HTML内容失败: {str(e)}")
                
                # 步骤3A: 创建HTML数据解析器
                self.logger.info("步骤3A: 初始化HTML数据解析器...")
                if progress_callback:
                    progress_callback(0, final_row_count, "正在初始化数据解析器...")
                
                try:
                    html_parser_manager = create_html_data_parser_manager()
                except Exception as e:
                    raise Exception(f"初始化HTML解析器失败: {str(e)}")
                
                # 步骤4A: 验证HTML结构
                self.logger.info("步骤4A: 验证HTML数据结构...")
                if progress_callback:
                    progress_callback(0, final_row_count, "正在验证数据结构...")
                
                validation_result = html_parser_manager.validate_html_structure(html_content)
                
                if not validation_result['is_valid']:
                    error_details = []
                    if not validation_result.get('header_found'):
                        error_details.append("未找到表头行")
                    if not validation_result.get('data_rows_found'):
                        error_details.append("未找到数据行")
                    if validation_result.get('missing_columns'):
                        error_details.append(f"缺少必要的数据列: {', '.join(validation_result['missing_columns'])}")
                    
                    error_msg = f"页面HTML结构验证失败:\n{'\n'.join(error_details)}"
                    error_msg += "\n\n可能的原因:\n1. 页面数据尚未加载完成\n2. 页面结构发生变化\n3. 当前页面不是数据列表页面"
                    raise Exception(error_msg)
                
                self.logger.info(f"HTML结构验证通过 - 找到{validation_result['total_rows']}行数据，{validation_result['total_columns']}列")
                
                # 如果数据行数很少，可能是虚拟滚动未完全加载
                if validation_result['total_rows'] < 10:
                    self.logger.warning(f"检测到数据行数较少({validation_result['total_rows']}行)，可能虚拟滚动未完全加载所有数据")
                
                # 步骤5A: 提取股票数据
                self.logger.info("步骤5A: 解析提取股票数据...")
                if progress_callback:
                    progress_callback(0, validation_result['total_rows'], f"正在解析{validation_result['total_rows']}条股票数据...")
                
                try:
                    raw_stock_data = html_parser_manager.parse_stock_data_from_html(html_content)
                except Exception as e:
                    raise Exception(f"解析股票数据失败: {str(e)}")
                
                if not raw_stock_data:
                    raise Exception("未能从页面中提取到股票数据，请确保:\n1. 页面已加载完成\n2. 包含股票数据表格\n3. 表格中有有效的股票信息")
                
                # 步骤6A: 转换数据格式
                self.logger.info("步骤6A: 转换数据格式...")
                if progress_callback:
                    progress_callback(0, len(raw_stock_data), f"正在转换{len(raw_stock_data)}条数据格式...")
                
                extracted_data = []
                invalid_count = 0
                
                for stock in raw_stock_data:
                    try:
                        stock_data = {
                            '股票代码': stock.get('stock_code', ''),
                            '股票名称': stock.get('stock_name', ''),
                            '小草竞王': stock.get('jingwang', ''),
                            '小草红盘起爆': stock.get('hongpan_qibao', ''),
                            '小草绿盘低吸': stock.get('lvpan_dixi', ''),
                            '小草连板接力': stock.get('lianban_jieli', '')
                        }
                        # 验证必要字段
                        if stock_data['股票代码'].strip() and stock_data['股票名称'].strip():
                            extracted_data.append(stock_data)
                        else:
                            invalid_count += 1
                    except Exception as e:
                        self.logger.warning(f"处理股票数据时出错: {e}")
                        invalid_count += 1
                
                if invalid_count > 0:
                    self.logger.warning(f"跳过了{invalid_count}条无效数据")
                    
            else:
                # 使用滚动过程中收集的数据
                self.logger.info(f"使用滚动过程中收集的 {len(collected_data)} 条数据")
                extracted_data = collected_data
                
                # 步骤3B: 数据验证和清洗
                self.logger.info("步骤3B: 验证和清洗预收集数据...")
                if progress_callback:
                    progress_callback(0, len(collected_data), f"正在验证{len(collected_data)}条预收集数据...")
                
                # 验证数据完整性
                valid_data = []
                invalid_count = 0
                
                for stock_data in collected_data:
                    try:
                        # 验证必要字段
                        if (stock_data.get('股票代码', '').strip() and 
                            stock_data.get('股票名称', '').strip()):
                            valid_data.append(stock_data)
                        else:
                            invalid_count += 1
                    except Exception as e:
                        self.logger.warning(f"验证股票数据时出错: {e}")
                        invalid_count += 1
                
                extracted_data = valid_data
                if invalid_count > 0:
                    self.logger.warning(f"从预收集数据中剔除了{invalid_count}条无效数据")
            
            # 最终完成
            success_message = f"数据抓取完成！实际获取{len(extracted_data)}条有效股票数据"
            if len(collected_data) >= 10:
                success_message += f"（使用滚动预收集数据）"
            else:
                success_message += f"（使用传统HTML解析，通过滚动加载获取了{final_row_count}行DOM数据）"
                
            self.logger.info(success_message)
            
            if progress_callback:
                progress_callback(0, len(extracted_data), success_message)
            
            return extracted_data
            
        except Exception as e:
            error_msg = f"数据抓取失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"抓取失败: {str(e)}")
            return []
    
    
    def _is_valid_value(self, value: str) -> bool:
        """
        检查数据值是否有效
        
        Args:
            value: 要检查的数据值
            
        Returns:
            bool: 如果值有效返回True，否则返回False
        """
        if not value:
            return False
        
        # 转换为字符串并去除空格
        str_value = str(value).strip()
        
        # 检查是否为配置中定义的无效值
        invalid_values = self.config.get('invalid_values', ['--', '0.00', '', ' ', 'null', 'undefined', 'N/A', '-'])
        
        # 检查是否为无效值
        if str_value in invalid_values:
            return False
        
        # 检查是否为空字符串
        if not str_value:
            return False
            
        return True
    
    def filter_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """筛选数据：只保留有分值的股票，排除--等无效数据"""
        if not self.config['filter_empty_values']:
            return data
        
        filtered_data = []
        target_fields = self.config['target_fields']
        invalid_data_count = 0  # 统计被排除的无效数据数量
        
        for stock in data:
            # 检查是否有任意一个目标字段有有效值
            has_valid_value = False
            for field in target_fields:
                if field in stock and self._is_valid_value(stock[field]):
                    has_valid_value = True
                    break
            
            if has_valid_value:
                filtered_data.append(stock)
            else:
                invalid_data_count += 1
        
        # 增强的日志记录
        self.logger.info(f"数据筛选完成：保留 {len(filtered_data)} 条有效数据，排除 {invalid_data_count} 条无效数据（包含'--'等）")
        return filtered_data
    
    def save_to_excel(self, data: List[Dict[str, Any]]) -> str:
        """保存数据到Excel文件"""
        try:
            if not data:
                self.logger.warning("没有数据需要保存")
                return ""
            
            # 创建输出目录
            output_dir = self.config['output_directory']
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.config['output_file_format'].format(timestamp=timestamp)
            filepath = os.path.join(output_dir, filename)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 确保列顺序
            target_fields = self.config['target_fields']
            columns = ['股票代码', '股票名称'] + target_fields
            
            # 重新排列列顺序
            df = df.reindex(columns=columns, fill_value='')
            
            # 保存到Excel
            df.to_excel(filepath, index=False, sheet_name='小草选股数据')
            
            self.logger.info(f"数据已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存Excel文件失败: {str(e)}")
            return ""
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            
            if self.context:
                await self.context.close()
            
            if self.browser:
                await self.browser.close()
            
            if self.playwright:
                await self.playwright.stop()
            
            self.logger.info("浏览器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭浏览器时出错: {str(e)}")
    
    async def run_full_process(self, username: str = "", password: str = ""):
        """运行完整的自动化流程"""
        try:
            # 初始化浏览器
            if not await self.initialize_browser():
                return False, "浏览器初始化失败"
            
            # 导航到网站
            if not await self.navigate_to_site():
                return False, "导航到网站失败"
            
            # 自动登录（如果提供了用户名密码）
            if username and password:
                if not await self.auto_login(username, password):
                    return False, "自动登录失败"
            
            return True, "初始化完成，等待用户手动导航到数据页面"
            
        except Exception as e:
            error_msg = f"自动化流程执行失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    async def extract_and_save(self, progress_callback=None):
        """
        抓取数据并保存
        
        Args:
            progress_callback: 进度回调函数，用于GUI更新
        """
        try:
            # 抓取数据（包含滚动加载功能）
            raw_data = await self.extract_stock_data(progress_callback)
            
            if not raw_data:
                return False, "未抓取到数据", None
            
            # 更新进度：数据筛选
            if progress_callback:
                progress_callback(0, len(raw_data), "正在筛选数据...")
            
            # 筛选数据
            filtered_data = self.filter_data(raw_data)
            
            if not filtered_data:
                return False, "筛选后没有有效数据", None
            
            # 更新进度：保存文件
            if progress_callback:
                progress_callback(0, len(filtered_data), "正在保存Excel文件...")
            
            # 保存到Excel
            filepath = self.save_to_excel(filtered_data)
            
            if not filepath:
                return False, "保存文件失败", None
            
            # 创建详细结果信息
            result_info = {
                'total_extracted': len(raw_data),
                'filtered_count': len(filtered_data),
                'output_file': filepath,
                'data_preview': filtered_data[:5] if len(filtered_data) > 5 else filtered_data,  # 显示前5条数据
                'has_data_fields': self._analyze_data_fields(filtered_data)
            }
            
            success_message = f"成功保存 {len(filtered_data)} 条数据到: {filepath}"
            
            # 最终进度更新
            if progress_callback:
                progress_callback(0, len(filtered_data), success_message)
            
            return True, success_message, result_info
            
        except Exception as e:
            error_msg = f"数据抓取和保存失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"抓取失败: {str(e)}")
            return False, error_msg, None
    
    def _analyze_data_fields(self, data):
        """分析数据字段统计信息，排除--等无效数据"""
        if not data:
            return {}
        
        target_fields = ['小草竞王', '小草红盘起爆', '小草绿盘低吸', '小草连板接力']
        field_stats = {}
        
        for field in target_fields:
            # 使用_is_valid_value()方法统计有效数据数量
            valid_count = sum(1 for stock in data if self._is_valid_value(stock.get(field, '')))
            field_stats[field] = {
                'count': valid_count,
                'percentage': round(valid_count / len(data) * 100, 1) if data else 0
            }
        
        return field_stats

    async def extract_indicator_stocks(self, target_indicator: str, progress_callback=None):
        """
        基于特定小草指标筛选并抓取股票数据
        
        Args:
            target_indicator: 目标指标名称 (如: "小草红盘起爆")
            progress_callback: 进度回调函数，用于GUI更新
        """
        try:
            self.logger.info(f"开始基于{target_indicator}指标的股票筛选抓取...")
            
            # 抓取数据（包含基于指标的滚动停止逻辑）
            raw_data = await self.extract_indicator_specific_data(target_indicator, progress_callback)
            
            if not raw_data:
                return False, f"未抓取到{target_indicator}相关数据", None
            
            # 更新进度：数据筛选
            if progress_callback:
                progress_callback(0, len(raw_data), f"正在筛选{target_indicator}数据...")
            
            # 筛选数据：只保留指定指标有有效分值的股票
            filtered_data = self.filter_data_by_indicator(raw_data, target_indicator)
            
            if not filtered_data:
                return False, f"筛选后没有{target_indicator}有效数据", None
            
            # 更新进度：保存文件
            if progress_callback:
                progress_callback(0, len(filtered_data), f"正在保存{target_indicator}筛选结果...")
            
            # 保存到Excel（文件名包含指标信息）
            filepath = self.save_indicator_results_to_excel(filtered_data, target_indicator)
            
            if not filepath:
                return False, "保存筛选结果文件失败", None
            
            # 创建详细结果信息
            result_info = {
                'total_extracted': len(raw_data),
                'filtered_count': len(filtered_data),
                'target_indicator': target_indicator,
                'output_file': filepath,
                'data_preview': filtered_data[:5] if len(filtered_data) > 5 else filtered_data,
                'indicator_stats': self._analyze_indicator_distribution(filtered_data, target_indicator)
            }
            
            # 检查是否为分拆文件
            if ';' in filepath:
                # 分拆文件情况
                file_paths = filepath.split(';')
                result_info['is_split'] = True
                result_info['split_files'] = file_paths
                result_info['split_count'] = len(file_paths)
                
                success_message = (f"成功筛选{target_indicator}，共{len(filtered_data)}条数据\n"
                                 f"由于数据量较大，已分拆为{len(file_paths)}个文件:\n" + 
                                 '\n'.join([f"- {os.path.basename(path)}" for path in file_paths]))
            else:
                # 单个文件情况
                result_info['is_split'] = False
                success_message = f"成功筛选{target_indicator}，保存 {len(filtered_data)} 条数据到: {filepath}"
            
            # 最终进度更新
            if progress_callback:
                progress_callback(0, len(filtered_data), success_message)
            
            return True, success_message, result_info
            
        except Exception as e:
            error_msg = f"{target_indicator}指标筛选抓取失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"筛选失败: {str(e)}")
            return False, error_msg, None
    
    async def extract_indicator_specific_data(self, target_indicator: str, progress_callback=None):
        """
        抓取特定指标的股票数据（带滚动停止逻辑）
        
        Args:
            target_indicator: 目标指标名称
            progress_callback: 进度回调函数
        """
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            if not self.is_ready_for_extraction:
                raise Exception("尚未准备好开始数据抓取")
            
            # 等待页面加载完成
            try:
                await self.page.wait_for_load_state('networkidle', timeout=300000)
            except Exception as e:
                self.logger.warning(f"等待页面加载超时: {e}, 继续尝试获取数据")
            
            self.logger.info(f"开始{target_indicator}数据抓取流程...")
            
            # 步骤1: 执行基于指标的滚动加载和数据收集
            self.logger.info(f"步骤1: 执行{target_indicator}虚拟滚动加载和数据收集，直到指标值为0...")
            if progress_callback:
                progress_callback(0, 0, f"开始{target_indicator}滚动加载...")
            
            scroll_success, scroll_message, final_row_count, collected_data = await self._scroll_until_indicator_zero(target_indicator, progress_callback)
            
            if not scroll_success:
                raise Exception(f"{target_indicator}滚动加载失败: {scroll_message}")
            
            self.logger.info(f"{target_indicator}滚动加载完成: {scroll_message}")
            if progress_callback:
                progress_callback(0, len(collected_data), f"{target_indicator}滚动加载完成，收集到{len(collected_data)}只股票")
            
            # 步骤2: 验证收集的数据
            self.logger.info(f"步骤2: 验证收集的{target_indicator}股票数据...")
            if progress_callback:
                progress_callback(0, len(collected_data), f"正在验证{len(collected_data)}只股票数据...")
            
            if not collected_data:
                raise Exception("滚动过程中未收集到股票数据")
            
            # 步骤3: 转换和验证数据格式
            self.logger.info(f"步骤3: 验证{target_indicator}数据格式...")
            if progress_callback:
                progress_callback(0, len(collected_data), f"正在验证{len(collected_data)}条数据格式...")
            
            extracted_data = []
            invalid_count = 0
            
            for stock in collected_data:
                try:
                    # 数据已经是正确的格式，只需要验证必要字段
                    stock_code = stock.get('股票代码', '').strip()
                    stock_name = stock.get('股票名称', '').strip()
                    
                    if stock_code and stock_name:
                        extracted_data.append(stock)
                    else:
                        invalid_count += 1
                        
                except Exception as e:
                    self.logger.warning(f"处理股票数据时出错: {e}")
                    invalid_count += 1
            
            if invalid_count > 0:
                self.logger.warning(f"验证过程中跳过了{invalid_count}条无效数据")
            
            success_message = f"{target_indicator}数据抓取完成！滚动过程中收集{len(extracted_data)}条有效股票数据"
            self.logger.info(success_message)
            
            if progress_callback:
                progress_callback(0, len(extracted_data), success_message)
            
            return extracted_data
            
        except Exception as e:
            error_msg = f"{target_indicator}数据抓取失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"抓取失败: {str(e)}")
            return []
    
    async def _scroll_until_indicator_zero(self, target_indicator: str, progress_callback=None):
        """
        滚动页面直到指定指标值为0（使用虚拟滚动并收集数据）
        
        Args:
            target_indicator: 目标指标名称
            progress_callback: 进度回调函数
            
        Returns:
            tuple: (success, message, final_row_count, collected_data)
        """
        try:
            # 从配置文件获取滚动停止条件配置
            scroll_config = self.config.get('indicator_filtering', {}).get('scroll_stop_conditions', {})
            max_scrolls = scroll_config.get('max_scroll_count', 200)
            zero_threshold = scroll_config.get('zero_threshold', 0.01)
            check_interval = scroll_config.get('check_interval', 2.0)
            scroll_distance = 800  # 虚拟滚动距离
            
            scroll_count = 0
            consecutive_no_new_data = 0  # 连续无新数据计数
            last_row_count = 0
            
            # 数据收集相关变量
            collected_data_set = set()  # 用于去重的股票代码集合
            all_collected_data = []  # 存储所有收集到的股票数据
            
            # 从配置文件获取指标映射
            indicator_mapping = self.config.get('indicator_filtering', {}).get('indicator_mapping', {})
            
            target_field = indicator_mapping.get(target_indicator)
            if not target_field:
                raise Exception(f"未知的指标名称: {target_indicator}")
            
            self.logger.info(f"开始虚拟滚动和数据收集，监控{target_indicator}指标值，阈值: {zero_threshold}...")
            
            # 创建虚拟滚动处理器
            from optimized_virtual_scroll import OptimizedVirtualScrollHandler
            virtual_scroll_handler = OptimizedVirtualScrollHandler(self.page, self.logger)
            
            # 尝试激活虚拟滚动
            activation_success = await virtual_scroll_handler._try_activate_virtual_scroll()
            if not activation_success:
                self.logger.warning("虚拟滚动激活失败，但继续尝试滚动")
            
            # 首先收集初始可见数据
            try:
                initial_data = await virtual_scroll_handler._collect_current_visible_data_with_retry()
                for stock_data in initial_data:
                    stock_code = stock_data.get('股票代码', '')
                    if stock_code and stock_code not in collected_data_set:
                        collected_data_set.add(stock_code)
                        all_collected_data.append(stock_data)
                self.logger.info(f"收集初始数据: {len(initial_data)}条，累计{len(all_collected_data)}条")
            except Exception as e:
                self.logger.warning(f"收集初始数据失败: {e}")
            
            while scroll_count < max_scrolls:
                scroll_count += 1
                
                # 执行虚拟滚动
                try:
                    scroll_result = await virtual_scroll_handler._perform_single_virtual_scroll(scroll_distance)
                    
                    if not scroll_result['success']:
                        self.logger.warning(f"第{scroll_count}次虚拟滚动失败: {scroll_result.get('error', 'Unknown error')}")
                        # 如果虚拟滚动失败，尝试回退到简单滚动
                        await self.page.evaluate("window.scrollBy(0, 400);")
                        await asyncio.sleep(check_interval)
                    else:
                        # 虚拟滚动成功，等待数据更新
                        await asyncio.sleep(check_interval)
                    
                    # 等待新数据加载
                    await self.page.wait_for_timeout(1000)
                    
                except Exception as e:
                    self.logger.warning(f"虚拟滚动操作失败: {e}")
                    # 回退到简单滚动
                    try:
                        await self.page.evaluate("window.scrollBy(0, 400);")
                        await asyncio.sleep(check_interval)
                    except Exception as fallback_error:
                        self.logger.error(f"回退滚动也失败: {fallback_error}")
                
                # 检查当前页面的指标值
                try:
                    current_indicator_values = await self._get_current_indicator_values(target_field)
                    current_rows = len(current_indicator_values)
                    
                    # 更新进度
                    if progress_callback:
                        first_value = current_indicator_values[0] if current_indicator_values else 0
                        progress_callback(scroll_count, current_rows, 
                                        f"虚拟滚动{scroll_count}次，{target_indicator}第一个股票指标值: {first_value:.2f}，已收集{len(all_collected_data)}只股票")
                    
                    # 按顺序检查：如果第一个股票的指标值小于阈值，立即停止滚动
                    if current_indicator_values and len(current_indicator_values) > 0:
                        first_indicator_value = current_indicator_values[0]
                        if abs(first_indicator_value) < zero_threshold:
                            self.logger.info(f"检测到{target_indicator}第一个股票指标值为{first_indicator_value:.2f}（<{zero_threshold}），停止滚动")
                            break
                        else:
                            self.logger.debug(f"第一个股票{target_indicator}指标值: {first_indicator_value:.2f}，继续滚动")
                    
                    # 收集当前可见的股票数据（在指标检查有效的情况下）
                    new_data_count = 0  # 本次滚动收集到的新数据数量
                    if current_indicator_values:  # 如果能正常获取指标值，说明页面数据正常
                        try:
                            current_data = await virtual_scroll_handler._collect_current_visible_data_with_retry()
                            for stock_data in current_data:
                                stock_code = stock_data.get('股票代码', '')
                                if stock_code and stock_code not in collected_data_set:
                                    collected_data_set.add(stock_code)
                                    all_collected_data.append(stock_data)
                                    new_data_count += 1
                            
                            if new_data_count > 0:
                                self.logger.info(f"✅ 第{scroll_count}次滚动收集{new_data_count}条新数据，累计{len(all_collected_data)}条")
                                consecutive_no_new_data = 0  # 重置连续无新数据计数
                            else:
                                consecutive_no_new_data += 1
                                self.logger.info(f"⏳ 第{scroll_count}次滚动无新数据，连续{consecutive_no_new_data}次无新数据，累计{len(all_collected_data)}条")
                                
                        except Exception as e:
                            self.logger.warning(f"第{scroll_count}次滚动后收集数据失败: {e}")
                            consecutive_no_new_data += 1
                    else:
                        consecutive_no_new_data += 1
                    
                    # 检查是否连续多次无新数据（基于新数据收集而不是DOM行数）
                    if consecutive_no_new_data >= 5:
                        self.logger.info(f"连续{consecutive_no_new_data}次滚动无新数据，停止滚动")
                        break
                        
                except Exception as e:
                    self.logger.warning(f"检查指标值时出错: {e}")
            
            # 收集最后一批数据
            try:
                final_data = await virtual_scroll_handler._collect_current_visible_data_with_retry()
                final_new_count = 0
                for stock_data in final_data:
                    stock_code = stock_data.get('股票代码', '')
                    if stock_code and stock_code not in collected_data_set:
                        collected_data_set.add(stock_code)
                        all_collected_data.append(stock_data)
                        final_new_count += 1
                if final_new_count > 0:
                    self.logger.info(f"收集最终数据: 新增{final_new_count}条，总计{len(all_collected_data)}条")
            except Exception as e:
                self.logger.warning(f"收集最终数据失败: {e}")
                    
            # 获取最终的行数
            final_row_count = await self._get_current_row_count()
            
            success_message = f"{target_indicator}虚拟滚动完成，共执行{scroll_count}次滚动，DOM中{final_row_count}行数据，实际收集{len(all_collected_data)}只股票"
            self.logger.info(success_message)
            
            return True, success_message, final_row_count, all_collected_data
            
        except Exception as e:
            error_msg = f"{target_indicator}虚拟滚动过程出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, 0, []
    
    async def _get_current_indicator_values(self, target_field: str):
        """获取当前页面上指定指标的所有值"""
        try:
            # 从配置文件获取指标字段到列索引的映射
            field_to_column = self.config.get('indicator_filtering', {}).get('field_to_column', {})
            
            column_index = field_to_column.get(target_field)
            if column_index is None:
                # 如果配置中没有找到，使用默认值
                default_mapping = {
                    'jingwang': 12,         # 小草竞王列索引
                    'hongpan_qibao': 13,    # 小草红盘起爆列索引  
                    'lvpan_dixi': 14,       # 小草绿盘低吸列索引
                    'lianban_jieli': 11     # 小草连板接力列索引
                }
                column_index = default_mapping.get(target_field, 13)  # 默认红盘起爆
                self.logger.warning(f"配置中未找到{target_field}的列索引，使用默认值: {column_index}")
            
            values = await self.page.evaluate(f"""
                () => {{
                    const rows = document.querySelectorAll('.c-table-row-body');
                    const values = [];
                    
                    for (const row of rows) {{
                        const cells = row.querySelectorAll('.c-table-cell');
                        if (cells.length > {column_index}) {{
                            const cellText = cells[{column_index}].innerText.trim();
                            // 尝试解析为数值
                            const numValue = parseFloat(cellText);
                            if (!isNaN(numValue)) {{
                                values.push(numValue);
                            }} else if (cellText && cellText !== '--' && cellText !== '0.00') {{
                                values.push(0.01);  // 有内容但无法解析为数字，给个小值
                            }} else {{
                                values.push(0.0);  // 空值或--，记为0
                            }}
                        }}
                    }}
                    
                    return values;
                }}
            """)
            
            return values if values else []
            
        except Exception as e:
            self.logger.warning(f"获取{target_field}指标值失败: {e}")
            return []
    
    async def _get_current_row_count(self):
        """获取当前页面的数据行数"""
        try:
            row_count = await self.page.evaluate("""
                () => {
                    return document.querySelectorAll('.c-table-row-body').length;
                }
            """)
            return row_count if row_count else 0
        except:
            return 0
    
    def filter_data_by_indicator(self, data: List[Dict[str, Any]], target_indicator: str) -> List[Dict[str, Any]]:
        """根据指定指标筛选数据：只保留该指标有有效分值的股票"""
        if not data:
            return []
        
        filtered_data = []
        invalid_data_count = 0
        
        for stock in data:
            # 检查指定指标是否有有效值
            indicator_value = stock.get(target_indicator, '').strip()
            
            if self._is_valid_value(indicator_value):
                filtered_data.append(stock)
            else:
                invalid_data_count += 1
        
        self.logger.info(f"{target_indicator}筛选完成：保留 {len(filtered_data)} 条有效数据，排除 {invalid_data_count} 条无效数据")
        return filtered_data
    
    def save_indicator_results_to_excel(self, data: List[Dict[str, Any]], target_indicator: str) -> str:
        """保存指标筛选结果到Excel文件"""
        try:
            if not data:
                self.logger.warning("没有数据需要保存")
                return ""
            
            # 创建输出目录
            output_dir = self.config['output_directory']
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 从配置文件获取输出配置
            output_config = self.config.get('indicator_filtering', {}).get('output_config', {})
            filename_template = output_config.get('filename_template', '小草{indicator}_筛选结果_{timestamp}.xlsx')
            sheet_name_template = output_config.get('sheet_name_template', '{indicator}筛选结果')
            
            # 检查是否需要分拆文件
            file_splitting_config = output_config.get('file_splitting', {})
            splitting_enabled = file_splitting_config.get('enabled', True)
            split_threshold = file_splitting_config.get('split_threshold', 30)
            part_suffix_template = file_splitting_config.get('part_suffix_template', '_part{part_number}')
            
            # 生成包含指标信息的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # 清理指标名称用于文件名
            clean_indicator_name = target_indicator.replace('小草', '').replace(' ', '_')
            
            # 生成工作表名称
            sheet_name = sheet_name_template.format(indicator=clean_indicator_name)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 从配置获取是否包含所有指标列
            include_all_indicators = output_config.get('include_all_indicators', True)
            
            if include_all_indicators:
                # 确保列顺序
                target_fields = self.config.get('target_fields', [
                    '小草竞王', '小草红盘起爆', '小草绿盘低吸', '小草连板接力'
                ])
                columns = ['股票代码', '股票名称'] + target_fields
            else:
                # 只包含基本信息和目标指标
                columns = ['股票代码', '股票名称', target_indicator]
            
            # 重新排列列顺序
            df = df.reindex(columns=columns, fill_value='')
            
            # 检查是否启用分拆文件
            data_count = len(df)
            if splitting_enabled:
                # 按配置分拆文件
                self.logger.info(f"数据量({data_count})，将按配置分拆为多个文件")
                
                # 计算每个文件的数据量（平均分配）
                files_needed = file_splitting_config.get('split_count', 3)  # 从配置读取分拆文件数量
                rows_per_file = data_count // files_needed
                remainder = data_count % files_needed
                
                saved_files = []
                
                for part_num in range(1, files_needed + 1):
                    # 计算当前文件的数据范围
                    start_idx = (part_num - 1) * rows_per_file
                    if part_num == files_needed:
                        # 最后一个文件包含剩余的所有数据
                        end_idx = data_count
                    else:
                        end_idx = start_idx + rows_per_file
                    
                    # 提取当前部分的数据
                    part_df = df.iloc[start_idx:end_idx].copy()
                    
                    # 生成分拆文件名
                    base_filename = filename_template.format(
                        indicator=clean_indicator_name,
                        timestamp=timestamp
                    )
                    # 移除.xlsx扩展名，添加部分后缀，再添加扩展名
                    name_without_ext = os.path.splitext(base_filename)[0]
                    part_suffix = part_suffix_template.format(part_number=part_num)
                    part_filename = f"{name_without_ext}{part_suffix}.xlsx"
                    part_filepath = os.path.join(output_dir, part_filename)
                    
                    # 保存当前部分到Excel
                    with pd.ExcelWriter(part_filepath, engine='openpyxl') as writer:
                        part_df.to_excel(writer, index=False, sheet_name=sheet_name)
                    
                    saved_files.append(part_filepath)
                    self.logger.info(f"第{part_num}部分({len(part_df)}条数据)已保存到: {part_filepath}")
                
                # 返回所有保存的文件路径（用分号分隔）
                return ';'.join(saved_files)
                
            else:
                # 不分拆，正常保存单个文件
                filename = filename_template.format(
                    indicator=clean_indicator_name,
                    timestamp=timestamp
                )
                filepath = os.path.join(output_dir, filename)
                
                # 保存到Excel
                with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name=sheet_name)
                
                self.logger.info(f"{target_indicator}筛选结果已保存到: {filepath}")
                return filepath
            
        except Exception as e:
            self.logger.error(f"保存{target_indicator}筛选结果失败: {str(e)}")
            return ""
    
    def _analyze_indicator_distribution(self, data: List[Dict[str, Any]], target_indicator: str) -> Dict[str, Any]:
        """分析指定指标的数值分布"""
        if not data:
            return {}
        
        values = []
        for stock in data:
            value_str = stock.get(target_indicator, '').strip()
            if self._is_valid_value(value_str):
                try:
                    # 尝试转换为数值
                    if value_str.replace('.', '').replace('-', '').isdigit():
                        values.append(float(value_str))
                except:
                    pass
        
        if not values:
            return {'count': 0, 'min': 0, 'max': 0, 'avg': 0}
        
        return {
            'count': len(values),
            'min': round(min(values), 2),
            'max': round(max(values), 2),
            'avg': round(sum(values) / len(values), 2)
        }