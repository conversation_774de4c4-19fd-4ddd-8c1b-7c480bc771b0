#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票信号识别增强解决方案
基于字体颜色分析结果的完整识别方案
"""

import cv2
import numpy as np
import json
import time
from typing import Tuple, Optional, Dict, List
from datetime import datetime
import logging

class EnhancedSignalRecognizer:
    """增强的股票信号识别器"""
    
    def __init__(self, config_file: str = None):
        """初始化识别器"""
        self.logger = logging.getLogger(__name__)
        
        # 基于实际分析的字体颜色配置
        self.font_color_config = {
            '清仓': {
                'primary_rgb': [47, 94, 56],      # 绿色系
                'hsv_range': {
                    'h': [60, 80],    # 绿色色相范围
                    's': [150, 255],  # 高饱和度
                    'v': [80, 150]    # 中等亮度
                },
                'rgb_tolerance': 40,
                'confidence_threshold': 0.6
            },
            '持仓': {
                'primary_rgb': [138, 82, 3],      # 橙色系
                'hsv_range': {
                    'h': [15, 30],    # 橙色色相范围
                    's': [200, 255],  # 高饱和度
                    'v': [120, 180]   # 中高亮度
                },
                'rgb_tolerance': 35,
                'confidence_threshold': 0.6
            },
            '开仓': {
                'primary_rgb': [106, 47, 52],     # 红色系
                'hsv_range': {
                    'h': [0, 10],     # 红色色相范围（低端）
                    's': [150, 255],  # 高饱和度
                    'v': [100, 160]   # 中等亮度
                },
                'rgb_tolerance': 35,
                'confidence_threshold': 0.6
            }
        }
        
        # 背景色过滤配置
        self.background_filters = {
            'black': {'max_rgb': [20, 20, 20]},
            'yellow': {'min_rgb': [200, 180, 0], 'max_rgb': [255, 255, 50]}
        }
        
        # OCR配置（作为辅助识别）
        self.ocr_keywords = {
            '清仓': ['清仓', '清', '仓'],
            '持仓': ['持仓', '持', '仓'],
            '开仓': ['开仓', '开', '仓']
        }
        
        # 统计信息
        self.stats = {
            'total_recognitions': 0,
            'color_success': 0,
            'ocr_success': 0,
            'hybrid_success': 0,
            'failures': 0
        }
    
    def is_background_pixel(self, rgb: np.ndarray) -> bool:
        """判断是否为背景像素"""
        r, g, b = rgb
        
        # 黑色背景
        if r <= 20 and g <= 20 and b <= 20:
            return True
        
        # 黄色背景
        if r >= 200 and g >= 180 and b <= 50:
            return True
        
        return False
    
    def extract_font_pixels(self, image: np.ndarray) -> np.ndarray:
        """提取字体像素，过滤背景"""
        img_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pixels = img_rgb.reshape(-1, 3)
        
        # 过滤背景像素
        font_pixels = []
        for pixel in pixels:
            if not self.is_background_pixel(pixel):
                font_pixels.append(pixel)
        
        return np.array(font_pixels) if font_pixels else np.array([]).reshape(0, 3)
    
    def calculate_color_distance(self, color1: List[int], color2: List[int]) -> float:
        """计算RGB颜色距离"""
        return np.sqrt(sum((c1 - c2) ** 2 for c1, c2 in zip(color1, color2)))
    
    def recognize_by_color(self, image: np.ndarray) -> Tuple[Optional[str], float, Dict]:
        """基于字体颜色识别信号"""
        try:
            # 提取字体像素
            font_pixels = self.extract_font_pixels(image)
            
            if len(font_pixels) == 0:
                return None, 0.0, {'error': 'no_font_pixels'}
            
            # 计算平均字体颜色
            avg_font_color = np.mean(font_pixels, axis=0).astype(int)
            
            # 与各信号类型的标准颜色比较
            best_match = None
            best_confidence = 0.0
            color_distances = {}
            
            for signal_type, config in self.font_color_config.items():
                primary_color = config['primary_rgb']
                tolerance = config['rgb_tolerance']
                threshold = config['confidence_threshold']
                
                # 计算颜色距离
                distance = self.calculate_color_distance(avg_font_color, primary_color)
                color_distances[signal_type] = distance
                
                # 计算置信度（距离越小置信度越高）
                if distance <= tolerance:
                    confidence = max(0.0, 1.0 - (distance / tolerance))
                    if confidence >= threshold and confidence > best_confidence:
                        best_match = signal_type
                        best_confidence = confidence
            
            return best_match, best_confidence, {
                'avg_font_color': avg_font_color.tolist(),
                'color_distances': color_distances,
                'font_pixel_count': len(font_pixels)
            }
            
        except Exception as e:
            self.logger.error(f"颜色识别失败: {e}")
            return None, 0.0, {'error': str(e)}
    
    def recognize_by_hsv(self, image: np.ndarray) -> Tuple[Optional[str], float, Dict]:
        """基于HSV颜色空间识别信号"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 提取非背景区域
            font_mask = np.ones(hsv.shape[:2], dtype=bool)
            
            # 过滤黑色背景
            black_mask = (hsv[:,:,2] <= 20)
            font_mask &= ~black_mask
            
            # 过滤黄色背景
            yellow_mask = ((hsv[:,:,0] >= 20) & (hsv[:,:,0] <= 30) & 
                          (hsv[:,:,1] >= 200) & (hsv[:,:,2] >= 180))
            font_mask &= ~yellow_mask
            
            if not np.any(font_mask):
                return None, 0.0, {'error': 'no_font_pixels_hsv'}
            
            # 统计各信号类型的匹配像素
            signal_matches = {}
            
            for signal_type, config in self.font_color_config.items():
                hsv_range = config['hsv_range']
                
                # 创建HSV范围掩码
                h_mask = ((hsv[:,:,0] >= hsv_range['h'][0]) & 
                         (hsv[:,:,0] <= hsv_range['h'][1]))
                s_mask = ((hsv[:,:,1] >= hsv_range['s'][0]) & 
                         (hsv[:,:,1] <= hsv_range['s'][1]))
                v_mask = ((hsv[:,:,2] >= hsv_range['v'][0]) & 
                         (hsv[:,:,2] <= hsv_range['v'][1]))
                
                # 组合掩码
                signal_mask = font_mask & h_mask & s_mask & v_mask
                match_count = np.sum(signal_mask)
                
                signal_matches[signal_type] = match_count
            
            # 找到最佳匹配
            if not signal_matches or max(signal_matches.values()) == 0:
                return None, 0.0, {'signal_matches': signal_matches}
            
            best_signal = max(signal_matches, key=signal_matches.get)
            total_font_pixels = np.sum(font_mask)
            confidence = signal_matches[best_signal] / total_font_pixels
            
            return best_signal, confidence, {
                'signal_matches': signal_matches,
                'total_font_pixels': total_font_pixels
            }
            
        except Exception as e:
            self.logger.error(f"HSV识别失败: {e}")
            return None, 0.0, {'error': str(e)}
    
    def recognize_signal(self, image_path: str, method: str = 'hybrid') -> Dict:
        """
        识别股票信号
        
        Args:
            image_path: 图片路径
            method: 识别方法 ('color', 'hsv', 'hybrid')
            
        Returns:
            识别结果字典
        """
        start_time = time.time()
        self.stats['total_recognitions'] += 1
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            return {
                'success': False,
                'error': 'cannot_read_image',
                'image_path': image_path
            }
        
        result = {
            'success': False,
            'signal': None,
            'confidence': 0.0,
            'method_used': method,
            'image_path': image_path,
            'processing_time': 0.0,
            'details': {}
        }
        
        try:
            if method in ['color', 'hybrid']:
                # RGB颜色识别
                signal, confidence, details = self.recognize_by_color(image)
                result['details']['color_recognition'] = details
                
                if signal and confidence >= 0.6:
                    result.update({
                        'success': True,
                        'signal': signal,
                        'confidence': confidence,
                        'method_used': 'color'
                    })
                    self.stats['color_success'] += 1
                    return result
            
            if method in ['hsv', 'hybrid']:
                # HSV颜色识别
                signal, confidence, details = self.recognize_by_hsv(image)
                result['details']['hsv_recognition'] = details
                
                if signal and confidence >= 0.3:
                    result.update({
                        'success': True,
                        'signal': signal,
                        'confidence': confidence,
                        'method_used': 'hsv'
                    })
                    self.stats['hybrid_success'] += 1
                    return result
            
            # 如果颜色识别失败，记录失败
            self.stats['failures'] += 1
            result['error'] = 'recognition_failed'
            
        except Exception as e:
            self.logger.error(f"信号识别异常: {e}")
            result['error'] = str(e)
            self.stats['failures'] += 1
        
        finally:
            result['processing_time'] = time.time() - start_time
        
        return result
    
    def get_statistics(self) -> Dict:
        """获取识别统计信息"""
        total = self.stats['total_recognitions']
        if total == 0:
            return self.stats
        
        return {
            **self.stats,
            'success_rate': (total - self.stats['failures']) / total,
            'color_success_rate': self.stats['color_success'] / total,
            'hybrid_success_rate': self.stats['hybrid_success'] / total
        }

def main():
    """测试函数"""
    recognizer = EnhancedSignalRecognizer()
    
    # 测试三张图片
    test_images = [
        'screenshots/qc2.png',  # 清仓
        'screenshots/cc2.png',  # 持仓
        'screenshots/kc2.png'   # 开仓
    ]
    
    print("=== 增强信号识别测试 ===\n")
    
    for image_path in test_images:
        print(f"测试图片: {image_path}")
        result = recognizer.recognize_signal(image_path, method='hybrid')
        
        if result['success']:
            print(f"  ✅ 识别成功: {result['signal']}")
            print(f"  置信度: {result['confidence']:.3f}")
            print(f"  方法: {result['method_used']}")
            print(f"  处理时间: {result['processing_time']:.3f}s")
        else:
            print(f"  ❌ 识别失败: {result.get('error', 'unknown')}")
        print()
    
    # 显示统计信息
    stats = recognizer.get_statistics()
    print("=== 识别统计 ===")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.3f}")
        else:
            print(f"{key}: {value}")

if __name__ == "__main__":
    main()
