# -*- coding: utf-8 -*-
"""
GUI 鼠标操作模块
包含鼠标坐标获取、位置重置等功能
"""

import threading
import time
import tkinter as tk
from tkinter import messagebox
import pyautogui
import random

from config import COMPASS_SOFTWARE, APP_CONFIG, update_mouse_target_position_config


class GUIMouseOperationsMixin:
    """GUI 鼠标操作Mixin类"""
    
    def load_saved_mouse_position(self):
        """读取配置文件中保存的鼠标基准位置"""
        try:
            if COMPASS_SOFTWARE.get('mouse_target_position'):
                position = COMPASS_SOFTWARE['mouse_target_position']
                x, y = position['x'], position['y']
                
                # 检查坐标是否有效（非默认值）
                if not (x == 0 and y == 0):
                    self.mouse_target_position = (x, y)
                    self.logger.info(f"加载已保存的鼠标基准位置: ({x}, {y})")
                    return True
                    
        except Exception as e:
            self.logger.error(f"读取鼠标基准位置配置失败: {str(e)}")
            
        return False
    
    def start_mouse_coordinate_capture(self):
        """开始获取鼠标坐标"""
        try:
            if self.mouse_tracking_active:
                self.logger.warning("鼠标坐标获取已在进行中")
                return
                
            self.logger.info("开始获取鼠标坐标，按回车键确认位置...")
            
            # 设置状态
            self.mouse_tracking_active = True
            
            # 更新按钮状态
            if hasattr(self, 'mouse_capture_btn'):
                self.mouse_capture_btn.config(text="获取中... (按回车确认)", state='disabled')
            
            # 更新状态显示
            if hasattr(self, 'mouse_position_var'):
                self.mouse_position_var.set("正在获取鼠标坐标... 按回车键确认")
            
            # 开始坐标跟踪线程
            self.mouse_tracking_thread = threading.Thread(target=self._track_mouse_position, daemon=True)
            self.mouse_tracking_thread.start()
            
            # 设置键盘监听
            self._setup_keyboard_listener()
                
        except Exception as e:
            self.logger.error(f"开始鼠标坐标获取失败: {str(e)}")
            messagebox.showerror("错误", f"开始鼠标坐标获取失败: {str(e)}")
            self._stop_mouse_tracking()
    
    def _track_mouse_position(self):
        """跟踪鼠标位置的线程函数"""
        try:
            while self.mouse_tracking_active:
                # 获取当前鼠标位置
                x, y = pyautogui.position()
                self.current_mouse_position = (x, y)
                
                # 更新界面显示（需要在主线程中执行）
                if hasattr(self, 'mouse_position_var'):
                    def update_display():
                        if self.mouse_tracking_active:
                            self.mouse_position_var.set(f"当前坐标: ({x}, {y}) - 按回车键确认")
                    
                    # 在主线程中更新UI
                    if hasattr(self, 'root'):
                        self.root.after(0, update_display)
                
                time.sleep(0.1)  # 100ms更新一次
                
        except Exception as e:
            self.logger.error(f"鼠标位置跟踪失败: {str(e)}")
    
    def _setup_keyboard_listener(self):
        """设置键盘监听"""
        try:
            # 绑定回车键事件到主窗口
            if hasattr(self, 'root'):
                self.root.bind('<Return>', self._on_enter_pressed)
                self.root.focus_set()  # 确保窗口获得焦点
                
        except Exception as e:
            self.logger.error(f"设置键盘监听失败: {str(e)}")
    
    def _on_enter_pressed(self, event=None):
        """回车键按下事件处理"""
        if self.mouse_tracking_active:
            self._confirm_mouse_position()
    
    def _confirm_mouse_position(self):
        """确认鼠标位置"""
        try:
            if not self.mouse_tracking_active:
                return
                
            x, y = self.current_mouse_position
            self.logger.info(f"确认鼠标基准位置: ({x}, {y})")
            
            # 保存位置
            self.mouse_target_position = (x, y)
            
            # 更新配置文件
            if update_mouse_target_position_config(x, y):
                self.logger.info("鼠标基准位置已保存到配置文件")
                
                # 更新界面显示
                if hasattr(self, 'mouse_position_var'):
                    self.mouse_position_var.set(f"基准位置: ({x}, {y})")
                
                messagebox.showinfo("成功", f"鼠标基准位置已设置为: ({x}, {y})")
            else:
                self.logger.error("保存鼠标基准位置到配置文件失败")
                messagebox.showerror("错误", "保存配置失败")
            
            # 停止跟踪
            self._stop_mouse_tracking()
                
        except Exception as e:
            self.logger.error(f"确认鼠标位置失败: {str(e)}")
            messagebox.showerror("错误", f"确认鼠标位置失败: {str(e)}")
            self._stop_mouse_tracking()
    
    def _stop_mouse_tracking(self):
        """停止鼠标坐标跟踪"""
        try:
            self.mouse_tracking_active = False
            
            # 解绑键盘事件
            if hasattr(self, 'root'):
                self.root.unbind('<Return>')
            
            # 恢复按钮状态
            if hasattr(self, 'mouse_capture_btn'):
                self.mouse_capture_btn.config(text="获取鼠标坐标", state='normal')
            
            # 如果没有保存的位置，显示默认信息
            if not self.mouse_target_position and hasattr(self, 'mouse_position_var'):
                self.mouse_position_var.set("未设置基准位置")
                
        except Exception as e:
            self.logger.error(f"停止鼠标跟踪失败: {str(e)}")
    
    def move_mouse_to_target_position(self):
        """将鼠标移动到基准位置（支持Y坐标随机偏移）"""
        try:
            # 检查是否启用鼠标定位功能
            if not APP_CONFIG.get('enable_mouse_positioning', True):
                self.logger.info("鼠标定位功能已禁用，跳过鼠标移动")
                return True  # 返回True表示操作成功（虽然被跳过了）
            
            if not self.mouse_target_position:
                self.logger.warning("未设置鼠标基准位置，跳过鼠标移动")
                return False
            
            # 获取基准坐标
            base_x, base_y = self.mouse_target_position
            
            # 计算实际移动目标坐标（支持Y坐标偏移）
            target_x = base_x  # X坐标保持不变
            target_y = base_y  # Y坐标默认保持不变
            
            # 检查是否启用Y坐标随机偏移
            offset_config = APP_CONFIG.get('mouse_reset_offset', {})
            if offset_config.get('enable_y_offset', False):
                offset_range = offset_config.get('y_offset_range', 20)
                # 生成-offset_range到+offset_range的随机偏移
                y_offset = random.randint(-offset_range, offset_range)
                target_y = base_y + y_offset
                self.logger.debug(f"Y坐标偏移: {y_offset}像素, 目标坐标: ({target_x}, {target_y})")
            
            self.logger.info(f"将鼠标移动到目标位置: ({target_x}, {target_y})")
            
            # 移动鼠标
            pyautogui.moveTo(target_x, target_y, duration=0)
            
            # 验证移动是否成功
            current_pos = pyautogui.position()
            if abs(current_pos.x - target_x) <= 2 and abs(current_pos.y - target_y) <= 2:
                # 鼠标移动成功，检查是否需要执行左键点击
                if APP_CONFIG.get('enable_mouse_click_on_reset', True):
                    try:
                        pyautogui.click()
                        self.logger.info("鼠标已成功移动到目标位置并执行点击")
                    except Exception as click_e:
                        self.logger.warning(f"在目标位置点击失败: {str(click_e)}")
                        self.logger.info("鼠标已成功移动到目标位置（点击失败）")
                        # 即使点击失败，仍然返回True，因为鼠标移动成功了
                else:
                    self.logger.info("鼠标已成功移动到目标位置（点击功能已禁用）")
                
                return True
            else:
                self.logger.warning(f"鼠标移动可能不准确，目标位置: ({target_x}, {target_y})，实际位置: ({current_pos.x}, {current_pos.y})")
                return False
                
        except Exception as e:
            self.logger.error(f"移动鼠标到目标位置失败: {str(e)}")
            return False
    
    def get_mouse_target_position_info(self):
        """获取鼠标基准位置信息文本"""
        if self.mouse_target_position:
            x, y = self.mouse_target_position
            return f"基准位置: ({x}, {y})"
        else:
            return "未设置基准位置"
    
    def reset_mouse_position_config(self):
        """重置鼠标基准位置配置"""
        try:
            self.mouse_target_position = None
            
            # 更新配置文件
            if update_mouse_target_position_config(0, 0):
                self.logger.info("鼠标基准位置配置已重置")
                
                # 更新界面显示
                if hasattr(self, 'mouse_position_var'):
                    self.mouse_position_var.set("未设置基准位置")
                
                messagebox.showinfo("成功", "鼠标基准位置已重置")
                return True
            else:
                self.logger.error("重置鼠标基准位置配置失败")
                messagebox.showerror("错误", "重置配置失败")
                return False
                
        except Exception as e:
            self.logger.error(f"重置鼠标基准位置失败: {str(e)}")
            messagebox.showerror("错误", f"重置失败: {str(e)}")
            return False