# -*- coding: utf-8 -*-
"""
多空资金OCR验证结果管理工具
用于管理和清理验证目录中的文件
"""

import os
import glob
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import json

class FundOCRValidationManager:
    """多空资金OCR验证结果管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_dir = "fund_ocr_validation"
        
    def list_validation_sessions(self) -> List[Dict[str, Any]]:
        """列出所有验证会话"""
        if not os.path.exists(self.validation_dir):
            return []
        
        sessions = {}
        
        # 扫描所有文件，按时间戳分组
        for file_path in glob.glob(os.path.join(self.validation_dir, "*")):
            filename = os.path.basename(file_path)
            
            # 提取时间戳（格式：YYYYMMDD_HHMMSS）
            if "_" in filename:
                timestamp_part = filename.split("_", 2)
                if len(timestamp_part) >= 2:
                    try:
                        timestamp = f"{timestamp_part[0]}_{timestamp_part[1]}"
                        datetime.strptime(timestamp, '%Y%m%d_%H%M%S')  # 验证格式
                        
                        if timestamp not in sessions:
                            sessions[timestamp] = {
                                'timestamp': timestamp,
                                'datetime': datetime.strptime(timestamp, '%Y%m%d_%H%M%S'),
                                'files': [],
                                'size_bytes': 0
                            }
                        
                        file_size = os.path.getsize(file_path)
                        sessions[timestamp]['files'].append({
                            'name': filename,
                            'path': file_path,
                            'size': file_size
                        })
                        sessions[timestamp]['size_bytes'] += file_size
                        
                    except ValueError:
                        continue
        
        # 转换为列表并按时间排序
        session_list = list(sessions.values())
        session_list.sort(key=lambda x: x['datetime'], reverse=True)
        
        return session_list
    
    def get_session_summary(self, timestamp: str) -> Dict[str, Any]:
        """获取指定会话的详细信息"""
        sessions = self.list_validation_sessions()
        
        for session in sessions:
            if session['timestamp'] == timestamp:
                # 尝试读取报告文件
                json_report_path = os.path.join(self.validation_dir, f'{timestamp}_validation_report.json')
                
                summary = {
                    'timestamp': timestamp,
                    'datetime': session['datetime'],
                    'files_count': len(session['files']),
                    'total_size': session['size_bytes'],
                    'files': session['files'],
                    'has_report': os.path.exists(json_report_path),
                    'report_data': None
                }
                
                if summary['has_report']:
                    try:
                        with open(json_report_path, 'r', encoding='utf-8') as f:
                            summary['report_data'] = json.load(f)
                    except Exception as e:
                        self.logger.warning(f"无法读取报告文件: {e}")
                
                return summary
        
        return None
    
    def clean_old_sessions(self, days_to_keep: int = 7) -> Dict[str, Any]:
        """清理指定天数之前的验证会话"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        sessions = self.list_validation_sessions()
        
        deleted_sessions = []
        deleted_files = 0
        deleted_size = 0
        
        for session in sessions:
            if session['datetime'] < cutoff_date:
                # 删除该会话的所有文件
                for file_info in session['files']:
                    try:
                        os.remove(file_info['path'])
                        deleted_files += 1
                        deleted_size += file_info['size']
                        self.logger.info(f"已删除文件: {file_info['name']}")
                    except Exception as e:
                        self.logger.error(f"删除文件失败 {file_info['name']}: {e}")
                
                deleted_sessions.append(session['timestamp'])
        
        return {
            'deleted_sessions': deleted_sessions,
            'deleted_files': deleted_files,
            'deleted_size': deleted_size
        }
    
    def delete_session(self, timestamp: str) -> bool:
        """删除指定的验证会话"""
        session = self.get_session_summary(timestamp)
        if not session:
            return False
        
        try:
            for file_info in session['files']:
                os.remove(file_info['path'])
                self.logger.info(f"已删除文件: {file_info['name']}")
            
            return True
        except Exception as e:
            self.logger.error(f"删除会话失败: {e}")
            return False
    
    def get_directory_stats(self) -> Dict[str, Any]:
        """获取验证目录统计信息"""
        if not os.path.exists(self.validation_dir):
            return {
                'exists': False,
                'total_sessions': 0,
                'total_files': 0,
                'total_size': 0
            }
        
        sessions = self.list_validation_sessions()
        total_files = sum(len(session['files']) for session in sessions)
        total_size = sum(session['size_bytes'] for session in sessions)
        
        return {
            'exists': True,
            'directory': self.validation_dir,
            'total_sessions': len(sessions),
            'total_files': total_files,
            'total_size': total_size,
            'size_mb': round(total_size / 1024 / 1024, 2),
            'oldest_session': sessions[-1]['datetime'] if sessions else None,
            'newest_session': sessions[0]['datetime'] if sessions else None
        }
    
    def print_summary(self):
        """打印验证目录摘要"""
        stats = self.get_directory_stats()
        
        print("=" * 60)
        print("多空资金OCR验证结果目录统计")
        print("=" * 60)
        
        if not stats['exists']:
            print("验证目录不存在或为空")
            return
        
        print(f"目录位置: {stats['directory']}")
        print(f"验证会话数: {stats['total_sessions']}")
        print(f"文件总数: {stats['total_files']}")
        print(f"占用空间: {stats['size_mb']} MB")
        
        if stats['oldest_session']:
            print(f"最早会话: {stats['oldest_session'].strftime('%Y-%m-%d %H:%M:%S')}")
        if stats['newest_session']:
            print(f"最新会话: {stats['newest_session'].strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n最近的验证会话:")
        print("-" * 40)
        
        sessions = self.list_validation_sessions()[:5]  # 显示最近5个
        for session in sessions:
            size_mb = round(session['size_bytes'] / 1024 / 1024, 2)
            print(f"{session['timestamp']}: {len(session['files'])} 文件, {size_mb} MB")


def main():
    """主函数 - 命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多空资金OCR验证结果管理工具')
    parser.add_argument('--list', action='store_true', help='列出所有验证会话')
    parser.add_argument('--stats', action='store_true', help='显示目录统计信息')
    parser.add_argument('--clean', type=int, metavar='DAYS', help='清理指定天数之前的会话')
    parser.add_argument('--delete', type=str, metavar='TIMESTAMP', help='删除指定时间戳的会话')
    
    args = parser.parse_args()
    
    manager = FundOCRValidationManager()
    
    if args.stats or (not args.list and not args.clean and not args.delete):
        manager.print_summary()
    
    if args.list:
        sessions = manager.list_validation_sessions()
        print(f"\n找到 {len(sessions)} 个验证会话:")
        for session in sessions:
            size_mb = round(session['size_bytes'] / 1024 / 1024, 2)
            print(f"  {session['timestamp']}: {len(session['files'])} 文件, {size_mb} MB")
    
    if args.clean:
        print(f"\n清理 {args.clean} 天前的验证会话...")
        result = manager.clean_old_sessions(args.clean)
        print(f"已删除 {len(result['deleted_sessions'])} 个会话")
        print(f"删除文件数: {result['deleted_files']}")
        print(f"释放空间: {round(result['deleted_size'] / 1024 / 1024, 2)} MB")
    
    if args.delete:
        print(f"\n删除会话: {args.delete}")
        if manager.delete_session(args.delete):
            print("删除成功")
        else:
            print("删除失败 - 会话不存在或删除出错")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()