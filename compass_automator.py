# -*- coding: utf-8 -*-
"""
指南针自动化模块
负责与指南针桌面软件的交互，获取股票多空资金数据
"""

import time
import logging
import subprocess
import os
from typing import Dict, Any, Optional, Tuple
import pywinauto
from pywinauto import Application, findwindows
from pywinauto.keyboard import send_keys
import psutil

from config import COMPASS_SOFTWARE, APP_CONFIG
from compass_data_extractor import CompassDataExtractor

class CompassAutomator:
    """指南针自动化控制类"""

    def __init__(self, ocr_manager=None, mouse_reset_callback=None):
        """
        初始化自动化控制器

        Args:
            ocr_manager: OCR管理器实例，用于页面加载检测
            mouse_reset_callback: 鼠标重置回调函数，用于在分析过程中重置鼠标位置
        """
        self.logger = logging.getLogger(__name__)
        self.app = None
        self.main_window = None
        self.is_connected = False
        self.ocr_manager = ocr_manager
        self.mouse_reset_callback = mouse_reset_callback

        # 初始化数据提取器
        self.data_extractor = CompassDataExtractor()
        
    
    def start_compass_software(self) -> bool:
        """
        连接指南针软件（键盘操作模式）
        
        Returns:
            是否连接成功
        """
        try:
            # 在键盘操作模式下，只需要找到指南针窗口即可
            self.logger.info("尝试连接到指南针软件（键盘操作模式）")
            
            # 检查软件是否已经运行
            if self._is_compass_running():
                self.logger.info("指南针软件已在运行")
                return self._connect_to_compass_simple()
            
            # 如果软件未运行，提示用户手动启动
            self.logger.warning("指南针软件未运行，请手动启动指南针软件后重试")
            self.logger.info("提示：本工具支持键盘操作模式，无需复杂的软件路径配置")
            return False
            
        except Exception as e:
            self.logger.error(f"连接指南针软件失败: {str(e)}")
            return False
    
    def _is_compass_running(self) -> bool:
        """检查指南针软件是否正在运行"""
        try:
            process_name = COMPASS_SOFTWARE['process_name']
            for process in psutil.process_iter(['name']):
                if process.info['name'] == process_name:
                    return True
            return False
        except Exception:
            return False
    
    def _connect_to_compass_simple(self) -> bool:
        """简化的连接方法（键盘操作模式）"""
        try:
            # 尝试多种可能的窗口标题
            possible_titles = [
                COMPASS_SOFTWARE['main_window_title'],
                "指南针",
                "Compass",
                "全赢",
                "数据分析"
            ]
            
            for title in possible_titles:
                try:
                    # 尝试连接到应用程序
                    self.app = Application(backend="uia").connect(title_re=f".*{title}.*")
                    self.main_window = self.app.window(title_re=f".*{title}.*")
                    
                    if self.main_window.exists():
                        self.is_connected = True
                        # 设置数据提取器的主窗口
                        self.data_extractor.set_main_window(self.main_window)
                        self.logger.info(f"成功连接到指南针软件，窗口标题包含: {title}")
                        return True
                        
                except Exception:
                    continue
            
            # 如果以上都失败，尝试更宽松的匹配
            try:
                windows = findwindows.find_windows()
                for handle in windows:
                    try:
                        app = Application(backend="uia").connect(handle=handle)
                        window = app.window(handle=handle)
                        title = window.window_text()
                        
                        if any(keyword in title for keyword in ["指南针", "compass", "全赢", "数据", "股票"]):
                            self.app = app
                            self.main_window = window
                            self.is_connected = True
                            # 设置数据提取器的主窗口
                            self.data_extractor.set_main_window(self.main_window)
                            self.logger.info(f"成功连接到指南针软件，窗口标题: {title}")
                            return True
                    except Exception:
                        continue
                        
            except Exception:
                pass
            
            self.logger.error("未找到指南针软件窗口，请确保软件已运行")
            return False
            
        except Exception as e:
            self.logger.error(f"连接指南针软件失败: {str(e)}")
            return False

    def _connect_to_compass(self) -> bool:
        """连接到指南针软件"""
        try:
            # 尝试连接到应用程序
            self.app = Application(backend="uia").connect(
                title_re=f".*{COMPASS_SOFTWARE['main_window_title']}.*"
            )
            
            # 获取主窗口
            self.main_window = self.app.window(
                title_re=f".*{COMPASS_SOFTWARE['main_window_title']}.*"
            )
            
            if self.main_window.exists():
                self.is_connected = True
                # 设置数据提取器的主窗口
                self.data_extractor.set_main_window(self.main_window)
                self.logger.info("成功连接到指南针软件主窗口")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"连接指南针软件失败: {str(e)}")
            return False
    
    def search_stock(self, stock_code: str, wait_for_load: bool = True) -> bool:
        """
        搜索股票

        Args:
            stock_code: 股票代码
            wait_for_load: 是否等待页面加载完成

        Returns:
            是否搜索成功
        """
        try:
            if not self.is_connected:
                self.logger.error("未连接到指南针软件")
                return False

            # 使用send_keys逐字输入股票代码，禁用VK_PACKET避免触发实时搜索
            # 先确保指南针窗口获得焦点
            self.main_window.set_focus()
            
            # 逐字符输入股票代码，每个字符间隔20ms
            for char in stock_code:
                send_keys(char, vk_packet=False)
                time.sleep(0.02)  # 20ms间隔，确保输入稳定
            
            # 发送回车键确认
            send_keys("{ENTER}", vk_packet=False)

            self.logger.info(f"已搜索股票: {stock_code}")

            # 页面加载检测
            if wait_for_load:
                return self._wait_for_page_load_after_search(stock_code)
            else:
                # 如果不等待页面加载，使用传统的固定等待时间
                time.sleep(APP_CONFIG.get('page_load_wait', 0.5))
                return True

        except Exception as e:
            self.logger.error(f"搜索股票 {stock_code} 失败: {str(e)}")
            return False

    def _wait_for_page_load_after_search(self, stock_code: str) -> bool:
        """
        搜索股票后等待页面加载完成

        Args:
            stock_code: 股票代码

        Returns:
            是否加载成功
        """
        try:
            # 检查是否启用页面加载检测
            from page_load_detector import is_page_load_detection_enabled, check_page_loading_status

            if not is_page_load_detection_enabled():
                self.logger.debug("页面加载检测已禁用，使用固定等待时间")
                time.sleep(APP_CONFIG.get('page_load_wait', 0.5))
                return True

            # 检查OCR管理器是否可用
            if not self.ocr_manager:
                self.logger.warning("OCR管理器未设置，无法进行页面加载检测，使用固定等待时间")
                time.sleep(APP_CONFIG.get('page_load_wait', 0.5))
                return True

            if not self.ocr_manager.is_initialized():
                self.logger.warning("OCR管理器未初始化，无法进行页面加载检测，使用固定等待时间")
                time.sleep(APP_CONFIG.get('page_load_wait', 0.5))
                return True

            # 执行页面加载检测
            self.logger.debug(f"开始检测股票 {stock_code} 页面加载状态...")

            # 获取配置的超时时间
            page_load_config = APP_CONFIG.get('page_load_detection', {})
            timeout = page_load_config.get('default_timeout', 10.0)

            # 执行检测
            load_success = check_page_loading_status(self.ocr_manager, timeout)

            if load_success:
                self.logger.info(f"股票 {stock_code} 页面加载完成")
            else:
                self.logger.warning(f"股票 {stock_code} 页面加载检测超时，但继续执行")

            return load_success

        except ImportError as e:
            self.logger.warning(f"无法导入页面加载检测模块: {str(e)}，使用固定等待时间")
            time.sleep(APP_CONFIG.get('page_load_wait', 0.5))
            return False  # 修复：异常时应返回False，让监控跳过该股票

        except Exception as e:
            self.logger.error(f"页面加载检测失败: {str(e)}，使用固定等待时间")
            time.sleep(APP_CONFIG.get('page_load_wait', 0.5))
            return False  # 修复：异常时应返回False，让监控跳过该股票
    
    def get_fund_flow_data(self, stock_code: str) -> Dict[str, float]:
        """
        获取股票的多空资金数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            包含三天资金数据的字典
        """
        try:
            if not self.is_connected:
                self.logger.error("未连接到指南针软件")
                return {}
            
            # 使用数据提取器获取资金数据
            return self.data_extractor.get_fund_flow_data(stock_code)
                
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} 资金数据失败: {str(e)}")
            return {}
    
    def analyze_single_stock(self, stock_code: str) -> Dict[str, Any]:
        """
        分析单只股票
        
        Args:
            stock_code: 股票代码
            
        Returns:
            分析结果字典
        """
        result = {
            'stock_code': stock_code,
            'status': '分析失败',
            'today_fund': 0.0,
            'yesterday_fund': 0.0,
            'day_before_yesterday_fund': 0.0
        }
        
        try:
            self.logger.info(f"开始分析股票: {stock_code}")
            
            # 搜索股票
            if not self.search_stock(stock_code):
                result['status'] = '搜索失败'
                self.logger.warning(f"股票 {stock_code} 搜索失败")
                return result
            
            # 等待0.5秒
            time.sleep(0.5)
            # 重置鼠标位置到基准位置（确保操作准确性）
            if self.mouse_reset_callback:
                try:
                    if self.mouse_reset_callback():
                        self.logger.debug(f"股票 {stock_code} 鼠标已重置到基准位置")
                    else:
                        self.logger.warning(f"股票 {stock_code} 鼠标位置重置失败或未设置基准位置")
                except Exception as e:
                    self.logger.error(f"股票 {stock_code} 重置鼠标位置时出错: {str(e)}")
            else:
                self.logger.debug(f"股票 {stock_code} 未设置鼠标重置回调，跳过鼠标重置")
            
            # 等待0.5秒
            time.sleep(0.5)

            # 获取资金流数据
            fund_data = self.get_fund_flow_data(stock_code)
            if fund_data:
                result.update(fund_data)  # 更新结果中的资金数据
                result['status'] = '分析成功'
                self.logger.info(f"股票 {stock_code} 分析成功，获取到 {len(fund_data)} 项资金数据")
            else:
                result['status'] = '获取资金数据失败'
                self.logger.warning(f"股票 {stock_code} 获取资金数据失败")
            
            return result
            
        except Exception as e:
            self.logger.error(f"分析股票 {stock_code} 时出错: {str(e)}")
            result['status'] = f'分析错误: {str(e)}'
            return result
    
    def check_connection_valid(self) -> bool:
        """
        检查与指南针软件的连接是否仍然有效
        
        Returns:
            连接是否有效
        """
        try:
            if not self.is_connected or not self.main_window:
                self.logger.debug("连接状态为False或窗口为空")
                return False
            
            # 尝试检查窗口是否仍然存在
            if not self.main_window.exists():
                self.logger.warning("指南针软件窗口已不存在")
                self.is_connected = False
                return False
            
            # 尝试获取窗口状态来验证连接
            try:
                self.main_window.is_enabled()
                self.logger.debug("指南针软件连接状态正常")
                return True
            except Exception:
                self.logger.warning("指南针软件窗口状态异常")
                self.is_connected = False
                return False
                
        except Exception as e:
            self.logger.warning(f"检查指南针连接状态失败: {str(e)}")
            self.is_connected = False
            return False
    
    def close_compass_software(self):
        """关闭指南针软件连接"""
        try:
            
            if self.app:
                self.app = None
            if self.main_window:
                self.main_window = None
            self.is_connected = False
            self.logger.info("已断开与指南针软件的连接")
        except Exception as e:
            self.logger.error(f"关闭指南针软件连接失败: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        self.close_compass_software()