# -*- coding: utf-8 -*-
"""
统一信号监控模块
继承基础监控类，通过配置参数实现买入和卖出信号的统一监控逻辑
支持自定义关注信号、通知方式和统计逻辑
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any

from base_signal_monitor import BaseSignalMonitor
from dingtalk_notifier import create_dingtalk_notifier_by_group


class UnifiedSignalMonitor(BaseSignalMonitor):
    """统一信号监控器"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any], signal_type: str = 'buy'):
        """
        初始化统一信号监控器
        
        Args:
            compass_automator: 指南针自动化器实例
            signal_analyzer: 信号分析器实例
            config: 监控配置
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        super().__init__(compass_automator, signal_analyzer, config)
        
        self.signal_type = signal_type
        self.signal_type_config = config.get('signal_type_configs', {}).get(signal_type, {})
        
        # 获取关注的主要信号
        self.primary_signal = self.signal_type_config.get('primary_signal', '开仓' if signal_type == 'buy' else '清仓')
        self.alert_signals = self.signal_type_config.get('alert_signals', [self.primary_signal])
        
        # 初始化钉钉通知器（使用选定的群）
        self.dingtalk_notifier = create_dingtalk_notifier_by_group()
        if self.dingtalk_notifier:
            self.logger.info("钉钉通知器初始化成功")
        else:
            self.logger.warning("钉钉通知器初始化失败，将跳过钉钉通知功能")
        
        self.logger.info(f"{self.get_signal_type()}信号监控器初始化完成，主要关注: {self.get_full_signal_name(self.primary_signal)}")
    
    def get_signal_type(self) -> str:
        """获取信号类型名称"""
        return "买入" if self.signal_type == 'buy' else "卖出"
    
    def get_full_signal_name(self, short_signal: str) -> str:
        """获取完整的信号名称"""
        if hasattr(self.signal_analyzer, 'get_full_signal_name'):
            return self.signal_analyzer.get_full_signal_name(short_signal)
        # 回退方案：简单映射
        mapping = {'持': '持仓', '开': '开仓', '空': '空仓', '清': '清仓'}
        return mapping.get(short_signal, short_signal)
    
    def _process_single_stock(self, stock_code: str, message_queue) -> bool:
        """
        处理单个股票的信号识别（统一处理同步版本）
        
        Args:
            stock_code: 股票代码
            message_queue: 消息队列
            
        Returns:
            是否处理成功
        """
        # 确保股票代码的独立性，避免引用问题
        current_stock_code = str(stock_code)
        
        try:
            self.logger.debug(f"开始处理股票: {current_stock_code}")
            
            # 搜索股票（包含页面加载检测）
            search_success = self.compass_automator.search_stock(current_stock_code, wait_for_load=True)
            if not search_success:
                message_queue.put(("log", f"❌ 股票 {current_stock_code} 搜索失败，跳过"))
                return False

            # 页面加载检测已在search_stock中完成，这里添加额外的等待时间（如果需要）
            additional_wait = self.config.get('page_switch_wait', 1.0)
            if additional_wait > 0:
                message_queue.put(("log", f"  ⏱️ 额外等待 {additional_wait} 秒确保页面稳定"))
                time.sleep(additional_wait)
            
            # 重置鼠标位置到基准位置（确保截图时鼠标在正确位置）
            try:
                if self._move_mouse_to_target_position():
                    message_queue.put(("log", f"  ✓ 鼠标已重置到基准位置"))
                else:
                    message_queue.put(("log", f"  ⚠ 鼠标位置重置失败或未设置基准位置"))
            except Exception as e:
                self.logger.error(f"重置鼠标位置时出错: {str(e)}")
                message_queue.put(("log", f"  ⚠ 鼠标位置重置异常: {str(e)}"))
            
            # 开盘热身功能：每只股票首次跳过信号识别，避免读取到昨日数据
            # 指南针软件在首次加载股票时会先显示前一交易日数据，然后跳转到当日
            if self.config.get('enable_stock_warmup', True) and current_stock_code not in self.signal_states:
                message_queue.put(("log", f"🔥 股票 {current_stock_code} 首次加载，执行热身跳过信号识别（避免读取昨日数据）"))
                
                # 记录热身状态，但不进行实际的信号识别
                # 这样下次处理该股票时就会正常进行信号识别
                self.signal_states[current_stock_code] = {
                    'is_warmup': True,  # 标记为热身状态
                    'warmup_time': time.strftime('%H:%M:%S'),
                    'warmup_round': self.current_round
                }

                time.sleep(1) # 等待1秒，确保热身完成
                
                # 返回True表示处理成功，但实际上是跳过了信号识别
                return True
            
            # 如果是热身后的第一次真正识别，清理热身标记
            if current_stock_code in self.signal_states and self.signal_states[current_stock_code].get('is_warmup', False):
                message_queue.put(("log", f"🎯 股票 {current_stock_code} 热身完成，开始正式信号识别"))
                # 清除热身状态，重新初始化
                del self.signal_states[current_stock_code]
            
            self.logger.debug(f"开始识别股票 {current_stock_code} 的信号")
            
            # OCR识别信号
            signal = self.signal_analyzer.recognize_signal(current_stock_code)
            if not signal:
                message_queue.put(("log", f"⚠️ 股票 {current_stock_code} 信号识别失败，跳过"))
                return False
            
            self.logger.debug(f"股票 {current_stock_code} 信号识别结果: {signal}")
            
            # 记录和检测信号变化（使用独立的股票代码）
            changed, change_info = self._record_signal_state(current_stock_code, signal)
            
            # 如果有变化，立即同步处理通知
            if changed:
                self.monitoring_stats['total_signal_changes'] += 1
                
                self.logger.debug(f"股票 {current_stock_code} 信号发生变化，开始同步处理")
                
                if change_info['is_first_record']:
                    self._handle_first_signal_detection(current_stock_code, signal, message_queue)
                else:
                    self._handle_signal_change(current_stock_code, change_info, message_queue)
                    
                self.logger.debug(f"股票 {current_stock_code} 信号变化处理完成")
            else:
                # 仅在调试模式下记录无变化的情况
                if self.config.get('log_all_signals', False):
                    full_signal_name = self.get_full_signal_name(signal)
                    message_queue.put(("log", f"📊 股票 {current_stock_code} 信号保持: {full_signal_name}"))
            
            self.logger.debug(f"股票 {current_stock_code} 处理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"处理股票 {current_stock_code} 失败: {str(e)}")
            return False
    
    def _handle_first_signal_detection(self, stock_code: str, signal: str, message_queue):
        """处理首次信号检测（同步版本）"""
        # 确保股票代码和信号的独立性
        current_stock_code = str(stock_code)
        current_signal = str(signal)
        
        full_signal_name = self.get_full_signal_name(current_signal)
        
        # 检查严格通知模式
        strict_mode = self.config.get('strict_notification_mode', True)
        
        if current_signal == self.primary_signal:
            if self.signal_type == 'buy':
                message_queue.put(("log", f"💡 股票 {current_stock_code} 首次记录到{full_signal_name}信号！"))
                self._send_buy_signal_notification(current_stock_code, current_signal, "首次检测")
            else:  # sell
                message_queue.put(("log", f"🚨 股票 {current_stock_code} 首次记录到{full_signal_name}信号！！危险提醒！！"))
                self._send_sell_signal_notification(current_stock_code, current_signal, "首次检测", old_signal=None, is_danger=True)
        elif not strict_mode and self.signal_type == 'buy' and current_signal == "清仓":
            # 非严格模式：买入监控中首次检测到清仓信号也发送钉钉
            message_queue.put(("log", f"🚨 股票 {current_stock_code} 首次记录到{full_signal_name}信号！！危险提醒！！"))
            self._send_buy_signal_notification(current_stock_code, current_signal, "首次检测")
        else:
            if strict_mode and self.signal_type == 'buy' and current_signal == "清仓":
                # 严格模式：买入监控中的清仓信号仅记录不通知
                message_queue.put(("log", f"📝 股票 {current_stock_code} 首次记录信号: {full_signal_name} (严格模式-静默记录)"))
            elif self.signal_type == 'sell':
                message_queue.put(("log", f"📝 股票 {current_stock_code} 首次记录信号: {full_signal_name} (静默记录)"))
            else:
                message_queue.put(("log", f"📝 股票 {current_stock_code} 首次记录信号: {full_signal_name}"))
    
    def _handle_signal_change(self, stock_code: str, change_info: Dict[str, Any], message_queue):
        """处理信号变化（同步版本）"""
        # 确保股票代码和信号的独立性
        current_stock_code = str(stock_code)
        old_signal = str(change_info['old_signal'])
        new_signal = str(change_info['new_signal'])
        
        old_full_name = self.get_full_signal_name(old_signal)
        new_full_name = self.get_full_signal_name(new_signal)
        
        # 检查严格通知模式
        strict_mode = self.config.get('strict_notification_mode', True)
        
        if new_signal == self.primary_signal:
            if self.signal_type == 'buy':
                message_queue.put(("log", f"🚀 股票 {current_stock_code} 信号变化: {old_full_name} → {new_full_name} ({new_full_name}信号！)"))
                self._send_buy_signal_notification(current_stock_code, new_signal, "信号变化", old_signal)
            else:  # sell
                message_queue.put(("log", f"🚨 股票 {current_stock_code} 信号变化: {old_full_name} → {new_full_name} ({new_full_name}信号！！危险！！)"))
                self._send_sell_signal_notification(current_stock_code, new_signal, "信号变化", old_signal, is_danger=True)
        elif self.signal_type == 'sell' and old_signal == '持' and new_signal == '清':
            # 特别处理：从持仓变为清仓的情况
            message_queue.put(("log", f"⚠️⚠️ 股票 {current_stock_code} 持仓股票发出卖出信号！！{old_full_name} → {new_full_name} ⚠️⚠️"))
            self._send_sell_signal_notification(current_stock_code, new_signal, "持仓转清仓", old_signal, is_danger=True)
        else:
            message_queue.put(("log", f"🔄 股票 {current_stock_code} 信号变化: {old_full_name} → {new_full_name}"))
            
            # 其他信号变化的通知（根据严格模式决定）
            should_notify = False
            if self.signal_type == 'buy':
                # 买入监控：严格模式下只通知主要信号变化，非严格模式通知所有变化
                should_notify = not strict_mode or new_signal == self.primary_signal
                if should_notify:
                    self._send_buy_signal_notification(current_stock_code, new_signal, "信号变化", old_signal)
            else:  # sell
                # 卖出监控：严格模式下只通知主要信号变化，非严格模式通知所有变化
                should_notify = not strict_mode or new_signal == self.primary_signal
                if should_notify:
                    self._send_sell_signal_notification(current_stock_code, new_signal, "信号变化", old_signal)
            
            # 添加日志说明是否发送通知
            if strict_mode and not should_notify:
                if self.signal_type == 'buy' and (old_signal == "清仓" or new_signal == "清仓"):
                    pass  # 清仓相关变化已在上面记录
                else:
                    message_queue.put(("log", f"    (严格模式-非主要信号变化，未发送钉钉通知)"))
    
    def _send_buy_signal_notification(self, stock_code: str, signal: str, change_type: str, old_signal: str = None):
        """发送买入信号通知（同步版本）"""
        if self.dingtalk_notifier:
            try:
                # 确保股票代码的独立性，避免引用问题
                stock_code_copy = str(stock_code)
                signal_copy = str(signal)
                change_type_copy = str(change_type)
                old_signal_copy = str(old_signal) if old_signal else None
                
                self.logger.debug(f"开始发送买入信号通知: 股票={stock_code_copy}, 信号={signal_copy}")
                
                # 同步发送通知，等待完成
                success = self.dingtalk_notifier.send_stock_signal_notification(
                    stock_code=stock_code_copy,
                    signal=signal_copy,
                    change_type=change_type_copy,
                    old_signal=old_signal_copy
                )
                
                if success:
                    self.logger.debug(f"股票 {stock_code_copy} 买入信号通知发送成功")
                else:
                    self.logger.warning(f"股票 {stock_code_copy} 买入信号通知发送失败")
                    
            except Exception as e:
                self.logger.error(f"发送钉钉通知失败: {str(e)}")
    
    def _send_sell_signal_notification(self, stock_code: str, signal: str, change_type: str, 
                                     old_signal: str = None, is_danger: bool = False):
        """发送卖出信号通知（同步版本）"""
        if self.dingtalk_notifier:
            try:
                # 确保股票代码的独立性，避免引用问题
                stock_code_copy = str(stock_code)
                signal_copy = str(signal)
                change_type_copy = str(change_type)
                old_signal_copy = str(old_signal) if old_signal else None
                
                self.logger.debug(f"开始发送卖出信号通知: 股票={stock_code_copy}, 信号={signal_copy}, 危险={is_danger}")
                
                # 同步发送通知，等待完成
                success = self.dingtalk_notifier.send_sell_signal_notification(
                    stock_code=stock_code_copy,
                    signal=signal_copy,
                    change_type=change_type_copy,
                    old_signal=old_signal_copy,
                    is_danger=is_danger
                )
                
                if success:
                    self.logger.debug(f"股票 {stock_code_copy} 卖出信号通知发送成功")
                else:
                    self.logger.warning(f"股票 {stock_code_copy} 卖出信号通知发送失败")
                    
            except Exception as e:
                self.logger.error(f"发送钉钉通知失败: {str(e)}")
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        获取监控统计信息
        
        Returns:
            统计信息字典
        """
        stats = super().get_monitoring_stats()
        
        if self.signal_type == 'buy':
            # 计算开仓信号的股票数量
            stats['stocks_with_open_signal'] = len([
                code for code, state in self.signal_states.items()
                if state['current_signal'] == '开'
            ])
        else:  # sell
            # 计算清仓信号的股票数量
            stats['stocks_with_sell_signal'] = len([
                code for code, state in self.signal_states.items()
                if state['current_signal'] == '清'
            ])
            
            # 计算从持仓变为清仓的股票数量
            stats['stocks_position_to_sell'] = len([
                code for code, state in self.signal_states.items()
                if (state.get('first_signal') == '持' and 
                    state.get('current_signal') == '清')
            ])
        
        return stats


# 为了向后兼容，创建买入和卖出信号监控器的包装类
class BuySignalMonitor(UnifiedSignalMonitor):
    """买入信号监控器（向后兼容包装）"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any]):
        super().__init__(compass_automator, signal_analyzer, config, signal_type='buy')


class SellSignalMonitor(UnifiedSignalMonitor):
    """卖出信号监控器（向后兼容包装）"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any]):
        super().__init__(compass_automator, signal_analyzer, config, signal_type='sell')