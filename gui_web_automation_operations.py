# -*- coding: utf-8 -*-
"""
网页自动化GUI操作混入类
提供网页自动化相关的界面组件和操作方法
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import asyncio
import logging
import os
import pandas as pd
from typing import Optional

from web_automator import WebAutomator
from credentials_manager import get_credentials_manager
from web_element_config_wizard import show_web_element_config_wizard
from web_element_selector import get_web_element_selector_manager
from html_data_parser import create_html_data_parser_manager
from data_config_wizard import show_data_config_wizard


class GUIWebAutomationOperationsMixin:
    """网页自动化GUI操作混入类"""
    
    def init_web_automation_components(self):
        """初始化网页自动化组件"""
        self.web_automator: Optional[WebAutomator] = None
        self.web_automation_thread: Optional[threading.Thread] = None
        self.web_loop: Optional[asyncio.AbstractEventLoop] = None
        
        # 初始化管理器
        self.credentials_manager = get_credentials_manager()
        self.selector_manager = get_web_element_selector_manager()
        self.html_parser_manager = create_html_data_parser_manager()
        
        # 创建新的事件循环用于异步操作
        def create_loop():
            self.web_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.web_loop)
            self.web_loop.run_forever()
        
        self.web_loop_thread = threading.Thread(target=create_loop, daemon=True)
        self.web_loop_thread.start()
        
        # 加载保存的凭据
        self._load_saved_credentials()
        
        # 检查配置状态
        self._check_config_status()
        
        self.logger.info("网页自动化组件初始化完成")
    
    def _load_saved_credentials(self):
        """加载保存的登录凭据"""
        try:
            username, password = self.credentials_manager.load_credentials()
            if username and password:
                if hasattr(self, 'web_username_entry'):
                    self.web_username_entry.delete(0, tk.END)
                    self.web_username_entry.insert(0, username)
                if hasattr(self, 'web_password_entry'):
                    self.web_password_entry.delete(0, tk.END)
                    self.web_password_entry.insert(0, password)
                if hasattr(self, 'save_credentials_var'):
                    self.save_credentials_var.set(True)
                self.logger.info("已加载保存的登录凭据")
        except Exception as e:
            self.logger.error(f"加载凭据失败: {str(e)}")
    
    def _check_config_status(self):
        """检查配置状态"""
        try:
            config = self.selector_manager.load_selectors_from_config()
            if config and hasattr(self, 'config_status_label'):
                self.config_status_label.config(text="已配置", foreground="green")
                if hasattr(self, 'start_browser_btn'):
                    self.start_browser_btn.config(state=tk.NORMAL)
        except Exception as e:
            self.logger.debug(f"检查配置状态失败: {str(e)}")
    
    def update_web_status(self, status: str, color: str = "black"):
        """更新网页自动化状态显示"""
        if hasattr(self, 'web_status_label'):
            self.web_status_label.config(text=f"状态: {status}", foreground=color)
            self.root.update_idletasks()
    
    def show_config_wizard(self):
        """显示配置向导"""
        try:
            def on_config_completed(config):
                """配置完成回调"""
                self.logger.info("网页元素配置完成")
                self._check_config_status()
                messagebox.showinfo("成功", "网页元素配置完成！现在可以使用自动登录功能了。")
            
            show_web_element_config_wizard(parent_window=self.root, callback=on_config_completed)
            
        except Exception as e:
            error_msg = f"显示配置向导失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def show_data_config_wizard(self):
        """显示数据配置向导"""
        try:
            def on_data_config_completed(config):
                """数据配置完成回调"""
                self.logger.info("数据元素配置完成")
                self._check_config_status()
                messagebox.showinfo("成功", 
                                  "数据元素配置完成！\n\n"
                                  "现在您可以：\n"
                                  "1. 启动浏览器并手动登录\n"
                                  "2. 导航到数据页面\n"
                                  "3. 点击'开始抓取数据'进行自动化数据提取")
            
            from data_config_wizard import show_data_config_wizard
            show_data_config_wizard(parent_window=self.root, callback=on_data_config_completed)
            
        except Exception as e:
            error_msg = f"显示数据配置向导失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def show_html_data_processor(self):
        """显示HTML数据处理器"""
        try:
            def on_html_processing_completed(result):
                """HTML数据处理完成回调"""
                if result and result.get('stock_data'):
                    self.logger.info(f"HTML数据处理完成，提取到 {len(result['stock_data'])} 条数据")
                    messagebox.showinfo("成功", 
                                      f"HTML数据处理完成！\n\n"
                                      f"提取到 {len(result['stock_data'])} 条股票数据\n"
                                      "数据已保存到Excel文件")
                else:
                    self.logger.warning("HTML数据处理未提取到数据")
            
            # 显示数据配置向导（包括HTML处理功能）
            show_data_config_wizard(parent_window=self.root, callback=on_html_processing_completed)
            
        except Exception as e:
            error_msg = f"显示HTML数据处理器失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def process_html_data_from_file(self):
        """从文件处理HTML数据"""
        try:
            # 请求用户选择HTML文件
            file_path = filedialog.askopenfilename(
                title="选择HTML文件",
                filetypes=[("HTML files", "*.html *.htm"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            self.update_web_status("正在处理HTML文件...", "orange")
            
            def process_html():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    
                    # 验证HTML结构
                    validation_result = self.html_parser_manager.validate_html_structure(html_content)
                    
                    if not validation_result['is_valid']:
                        error_msg = f"HTML结构验证失败: {validation_result.get('error', '未知错误')}"
                        self.root.after(0, self._on_html_processing_error, error_msg)
                        return
                    
                    # 提取股票数据
                    stock_data = self.html_parser_manager.parse_stock_data_from_html(html_content)
                    
                    if stock_data:
                        # 保存到Excel文件
                        output_file = f"web_automation_results/html_extracted_data_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                        os.makedirs("web_automation_results", exist_ok=True)
                        
                        success = self.html_parser_manager.export_to_excel(html_content, output_file)
                        
                        if success:
                            result = {
                                'stock_data': stock_data,
                                'output_file': output_file,
                                'validation': validation_result
                            }
                            self.root.after(0, self._on_html_processing_completed, result)
                        else:
                            self.root.after(0, self._on_html_processing_error, "数据导出失败")
                    else:
                        self.root.after(0, self._on_html_processing_error, "未提取到任何股票数据")
                        
                except Exception as e:
                    error_msg = f"HTML文件处理失败: {str(e)}"
                    self.root.after(0, self._on_html_processing_error, error_msg)
            
            # 在后台线程中处理
            processing_thread = threading.Thread(target=process_html, daemon=True)
            processing_thread.start()
            
        except Exception as e:
            error_msg = f"选择HTML文件失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def _on_html_processing_completed(self, result):
        """HTML数据处理完成回调"""
        try:
            stock_data = result['stock_data']
            output_file = result['output_file']
            validation = result['validation']
            
            self.update_web_status("HTML数据处理完成", "green")
            
            message = (f"HTML数据处理成功！\n\n"
                      f"提取到 {len(stock_data)} 只股票数据\n"
                      f"数据已保存到: {output_file}\n\n"
                      f"验证信息:\n"
                      f"- 表格列数: {validation['total_columns']}\n"
                      f"- 数据行数: {validation['total_rows']}\n"
                      f"- 目标列: {', '.join(validation['target_columns_found'])}")
            
            messagebox.showinfo("处理成功", message)
            
            # 询问是否打开输出文件夹
            if messagebox.askyesno("提示", "是否打开输出文件夹？"):
                self.open_output_folder()
                
        except Exception as e:
            error_msg = f"处理HTML结果时出错: {str(e)}"
            self.logger.error(error_msg)
            self.update_web_status("处理结果时出错", "red")
    
    def _on_html_processing_error(self, error_msg):
        """HTML数据处理错误回调"""
        self.update_web_status("HTML数据处理失败", "red")
        self.logger.error(error_msg)
        messagebox.showerror("处理失败", error_msg)
    
    def validate_html_structure_from_file(self):
        """从文件验证HTML结构"""
        try:
            # 请求用户选择HTML文件
            file_path = filedialog.askopenfilename(
                title="选择HTML文件进行验证",
                filetypes=[("HTML files", "*.html *.htm"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 验证HTML结构
            validation_result = self.html_parser_manager.validate_html_structure(html_content)
            
            if validation_result['is_valid']:
                message = (f"HTML结构验证通过！\n\n"
                          f"表格信息:\n"
                          f"- 找到表头: {validation_result['header_found']}\n"
                          f"- 找到数据行: {validation_result['data_rows_found']}\n"
                          f"- 总列数: {validation_result['total_columns']}\n"
                          f"- 总行数: {validation_result['total_rows']}\n"
                          f"- 目标列: {', '.join(validation_result['target_columns_found'])}")
                
                if validation_result['missing_columns']:
                    message += f"\n- 缺少列: {', '.join(validation_result['missing_columns'])}"
                
                messagebox.showinfo("验证成功", message)
            else:
                error_msg = validation_result.get('error', '未知验证错误')
                messagebox.showerror("验证失败", f"HTML结构验证失败！\n\n错误: {error_msg}")
                
        except Exception as e:
            error_msg = f"HTML结构验证失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("验证失败", error_msg)
    
    def on_save_credentials_changed(self):
        """处理保存凭据选项变化"""
        try:
            if hasattr(self, 'save_credentials_var') and self.save_credentials_var.get():
                # 如果勾选了保存凭据，立即保存当前输入的凭据
                username = self.web_username_entry.get().strip()
                password = self.web_password_entry.get().strip()
                if username and password:
                    success = self.credentials_manager.save_credentials(username, password)
                    if success:
                        self.logger.info("登录凭据已保存")
                    else:
                        self.logger.warning("保存登录凭据失败")
        except Exception as e:
            self.logger.error(f"处理凭据保存选项失败: {str(e)}")
    
    def clear_saved_credentials(self):
        """清除保存的凭据"""
        try:
            result = messagebox.askyesno("确认", "确定要清除保存的登录信息吗？")
            if result:
                success = self.credentials_manager.clear_credentials()
                if success:
                    self.web_username_entry.delete(0, tk.END)
                    self.web_password_entry.delete(0, tk.END)
                    if hasattr(self, 'save_credentials_var'):
                        self.save_credentials_var.set(False)
                    self.logger.info("已清除保存的登录凭据")
                    messagebox.showinfo("成功", "已清除保存的登录信息")
                else:
                    messagebox.showerror("错误", "清除登录信息失败")
        except Exception as e:
            error_msg = f"清除凭据失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
        self.root.update_idletasks()
    
    def start_web_automation(self):
        """启动网页自动化"""
        def run_automation():
            try:
                # 创建WebAutomator实例
                self.web_automator = WebAutomator()
                
                # 获取登录信息
                username = self.web_username_entry.get().strip()
                password = self.web_password_entry.get().strip()
                
                # 在异步循环中运行
                future = asyncio.run_coroutine_threadsafe(
                    self.web_automator.run_full_process(username, password),
                    self.web_loop
                )
                
                success, message = future.result(timeout=60)  # 60秒超时
                
                # 更新UI
                self.root.after(0, self._on_automation_started, success, message)
                
            except Exception as e:
                error_msg = f"启动网页自动化失败: {str(e)}"
                self.logger.error(error_msg)
                self.root.after(0, self._on_automation_started, False, error_msg)
        
        # 更新状态
        self.update_web_status("正在启动浏览器...", "orange")
        self.start_browser_btn.config(state=tk.DISABLED)
        
        # 在后台线程中运行
        self.web_automation_thread = threading.Thread(target=run_automation, daemon=True)
        self.web_automation_thread.start()
    
    def _on_automation_started(self, success: bool, message: str):
        """处理自动化启动结果"""
        if success:
            self.update_web_status("浏览器已启动，用户名密码已填写，请手动点击登录", "green")
            self.start_extraction_btn.config(state=tk.NORMAL)
            self.get_indicator_btn.config(state=tk.NORMAL)  # 启用指标筛选按钮
            self.close_browser_btn.config(state=tk.NORMAL)
            
            # 显示提示信息 - 已注释掉以减少无用操作
            # messagebox.showinfo("提示", 
            #                   f"{message}\n\n"
            #                   "请在浏览器中手动完成以下操作:\n"
            #                   "1. 系统已自动填写用户名和密码，请手动点击登录按钮\n"
            #                   "2. 等待登录成功后，导航到包含小草选股数据的页面\n"
            #                   "3. 确保数据已加载完成\n"
            #                   "4. 点击'开始抓取数据'按钮")
        else:
            self.update_web_status("启动失败", "red")
            self.start_browser_btn.config(state=tk.NORMAL)
            messagebox.showerror("错误", message)
    
    def start_data_extraction(self):
        """开始数据抓取（支持滚动加载进度显示）"""
        def progress_callback(scroll_count, current_rows, message):
            """滚动加载进度回调函数 - 增强版本"""
            try:
                # 确保message不为空
                if not message:
                    message = f"正在滚动加载... 第{scroll_count}次"
                
                # 添加详细的进度信息
                detailed_message = f"{message} (已获取{current_rows}条数据)"
                
                # 在主线程中更新UI状态 - 增加重试机制
                def update_ui():
                    try:
                        if hasattr(self, 'update_web_status'):
                            self.update_web_status(detailed_message, "orange")
                        # 强制刷新界面
                        if hasattr(self, 'root') and self.root:
                            self.root.update_idletasks()
                    except Exception as ui_e:
                        # UI更新失败，至少要记录日志
                        if hasattr(self, 'logger'):
                            self.logger.warning(f"UI进度更新失败: {ui_e}")
                        print(f"进度更新失败: {ui_e}")  # 备用日志输出
                
                # 使用after方法在主线程中执行，增加错误重试
                if hasattr(self, 'root') and self.root:
                    self.root.after(0, update_ui)
                else:
                    # 如果root不存在，至少输出到控制台
                    print(f"进度: {detailed_message}")
                    
            except Exception as e:
                # 进度回调本身出错，不能影响主流程
                error_msg = f"进度回调异常: {e}"
                try:
                    if hasattr(self, 'logger'):
                        self.logger.error(error_msg)
                    else:
                        print(error_msg)
                except:
                    pass  # 连日志都写不了，就静默失败
        
        def run_extraction():
            try:
                if not self.web_automator:
                    raise Exception("网页自动化器未初始化")
                
                # 设置准备抓取状态
                self.web_automator.set_ready_for_extraction()
                
                self.logger.info("开始数据抓取，包含滚动加载功能...")
                
                # 在异步循环中运行抓取（传递进度回调）
                future = asyncio.run_coroutine_threadsafe(
                    self.web_automator.extract_and_save(progress_callback),
                    self.web_loop
                )
                
                # 增加超时时间以支持大量数据的滚动加载
                result = future.result(timeout=600)  # 10分钟超时，适应大量数据滚动加载
                
                # 处理新的返回格式
                if len(result) == 3:
                    success, message, result_info = result
                else:
                    # 兼容旧格式
                    success, message = result
                    result_info = None
                
                # 更新UI
                self.root.after(0, self._on_extraction_completed, success, message, result_info)
                
            except Exception as e:
                error_msg = f"数据抓取失败: {str(e)}"
                self.logger.error(error_msg)
                self.root.after(0, self._on_extraction_completed, False, error_msg, None)
        
        # 更新状态
        self.update_web_status("正在初始化数据抓取...", "orange")
        self.start_extraction_btn.config(state=tk.DISABLED)
        
        # 在后台线程中运行
        extraction_thread = threading.Thread(target=run_extraction, daemon=True)
        extraction_thread.start()
    
    def _on_extraction_completed(self, success: bool, message: str, result_info=None):
        """处理数据抓取结果"""
        if success:
            self.update_web_status("数据抓取完成", "green")
            
            # 构建详细的成功消息
            detailed_message = message
            
            if result_info:
                detailed_message += f"\n\n数据统计:"
                detailed_message += f"\n- 总数据条数: {result_info['total_extracted']}"
                detailed_message += f"\n- 筛选后数据: {result_info['filtered_count']}"
                
                # 数据字段统计
                if result_info.get('has_data_fields'):
                    detailed_message += "\n\n各数据字段统计:"
                    for field, stats in result_info['has_data_fields'].items():
                        detailed_message += f"\n- {field}: {stats['count']}条 ({stats['percentage']}%)"
                
                # 数据预览
                if result_info.get('data_preview'):
                    detailed_message += "\n\n数据预览(前5条):"
                    for i, stock in enumerate(result_info['data_preview'], 1):
                        detailed_message += f"\n{i}. {stock.get('股票代码', '')}-{stock.get('股票名称', '')}"
                        # 显示有数值的字段
                        data_fields = []
                        for field in ['小草竞王', '小草红盘起爆', '小草绿盘低吸', '小草连板接力']:
                            value = stock.get(field, '').strip()
                            if value and value != '0.00':
                                data_fields.append(f"{field}:{value}")
                        if data_fields:
                            detailed_message += f" ({', '.join(data_fields)})"
            
            messagebox.showinfo("成功", detailed_message)
            
            # 询问是否打开输出文件夹
            if messagebox.askyesno("提示", "数据抓取完成！是否打开输出文件夹？"):
                self.open_output_folder()
        else:
            self.update_web_status("数据抓取失败", "red")
            messagebox.showerror("错误", message)
        
        self.start_extraction_btn.config(state=tk.NORMAL)
    
    def start_indicator_extraction(self):
        """开始小草指标筛选抓取"""
        def progress_callback(scroll_count, current_rows, message):
            """指标筛选进度回调函数"""
            try:
                # 确保message不为空
                if not message:
                    message = f"正在筛选指标数据... 第{scroll_count}次滚动"
                
                # 添加详细的进度信息
                detailed_message = f"{message} (已获取{current_rows}条数据)"
                
                # 在主线程中更新UI状态
                def update_ui():
                    try:
                        if hasattr(self, 'update_indicator_status'):
                            self.update_indicator_status(detailed_message, "orange")
                        elif hasattr(self, 'update_web_status'):
                            self.update_web_status(detailed_message, "orange")
                        # 强制刷新界面
                        if hasattr(self, 'root') and self.root:
                            self.root.update_idletasks()
                    except Exception as ui_e:
                        if hasattr(self, 'logger'):
                            self.logger.warning(f"指标筛选UI进度更新失败: {ui_e}")
                
                # 使用after方法在主线程中执行
                if hasattr(self, 'root') and self.root:
                    self.root.after(0, update_ui)
                else:
                    print(f"指标筛选进度: {detailed_message}")
                    
            except Exception as e:
                # 进度回调出错，不影响主流程
                error_msg = f"指标筛选进度回调异常: {e}"
                try:
                    if hasattr(self, 'logger'):
                        self.logger.error(error_msg)
                    else:
                        print(error_msg)
                except:
                    pass
        
        def run_indicator_extraction():
            try:
                if not self.web_automator:
                    raise Exception("网页自动化器未初始化")
                
                # 获取选择的指标
                selected_indicator = self.selected_indicator_var.get()
                if not selected_indicator:
                    raise Exception("请选择要筛选的小草指标")
                
                # 设置准备抓取状态
                self.web_automator.set_ready_for_extraction()
                
                self.logger.info(f"开始{selected_indicator}指标筛选抓取...")
                
                # 在异步循环中运行指标筛选抓取（传递进度回调）
                future = asyncio.run_coroutine_threadsafe(
                    self.web_automator.extract_indicator_stocks(selected_indicator, progress_callback),
                    self.web_loop
                )
                
                # 指标筛选可能需要更长时间，设置10分钟超时
                result = future.result(timeout=600)
                
                # 处理返回格式
                if len(result) == 3:
                    success, message, result_info = result
                else:
                    # 兼容旧格式
                    success, message = result
                    result_info = None
                
                # 更新UI
                self.root.after(0, self._on_indicator_extraction_completed, success, message, result_info)
                
            except Exception as e:
                error_msg = f"{selected_indicator}指标筛选失败: {str(e)}"
                self.logger.error(error_msg)
                self.root.after(0, self._on_indicator_extraction_completed, False, error_msg, None)
        
        # 获取选择的指标
        selected_indicator = self.selected_indicator_var.get()
        if not selected_indicator:
            messagebox.showwarning("提示", "请先选择要筛选的小草指标")
            return
        
        # 更新状态
        self.update_indicator_status("正在初始化指标筛选...", "orange")
        self.get_indicator_btn.config(state=tk.DISABLED)
        
        # 在后台线程中运行
        indicator_thread = threading.Thread(target=run_indicator_extraction, daemon=True)
        indicator_thread.start()
    
    def _on_indicator_extraction_completed(self, success: bool, message: str, result_info=None):
        """处理指标筛选抓取结果"""
        if success:
            self.update_indicator_status("指标筛选完成", "green")
            
            # 构建详细的成功消息
            detailed_message = message
            
            if result_info:
                detailed_message += f"\n\n筛选统计:"
                detailed_message += f"\n- 总数据条数: {result_info['total_extracted']}"
                detailed_message += f"\n- 有效筛选数据: {result_info['filtered_count']}"
                detailed_message += f"\n- 筛选指标: {result_info.get('target_indicator', '未知')}"
                
                # 检查是否为分拆文件
                if result_info.get('is_split', False):
                    detailed_message += f"\n\n文件分拆信息:"
                    detailed_message += f"\n- 分拆原因: 按配置设置自动分拆为多个文件"
                    detailed_message += f"\n- 分拆文件数量: {result_info.get('split_count', 0)}个"
                    detailed_message += f"\n- 每个文件数据量: 约{result_info['filtered_count'] // result_info.get('split_count', 1)}条"
                    
                    if result_info.get('split_files'):
                        detailed_message += f"\n\n生成的文件:"
                        for i, file_path in enumerate(result_info['split_files'], 1):
                            import os
                            filename = os.path.basename(file_path)
                            detailed_message += f"\n  {i}. {filename}"
                else:
                    # 单个文件情况
                    if result_info.get('output_file'):
                        import os
                        filename = os.path.basename(result_info['output_file'])
                        detailed_message += f"\n\n输出文件: {filename}"
                
                # 数据预览
                if result_info.get('data_preview'):
                    detailed_message += "\n\n筛选结果预览(前5条):"
                    for i, stock in enumerate(result_info['data_preview'], 1):
                        stock_code = stock.get('股票代码', '')
                        stock_name = stock.get('股票名称', '')
                        indicator_value = stock.get(result_info.get('target_indicator', ''), '')
                        detailed_message += f"\n{i}. {stock_code}-{stock_name} ({result_info.get('target_indicator', '')}:{indicator_value})"
            
            messagebox.showinfo("筛选成功", detailed_message)
            
            # 询问是否打开输出文件夹
            if messagebox.askyesno("提示", "指标筛选完成！是否打开输出文件夹？"):
                self.open_output_folder()
        else:
            self.update_indicator_status("指标筛选失败", "red")
            messagebox.showerror("错误", message)
        
        self.get_indicator_btn.config(state=tk.NORMAL)
    
    def update_indicator_status(self, status: str, color: str = "black"):
        """更新指标筛选状态显示"""
        if hasattr(self, 'indicator_extraction_status_var'):
            self.indicator_extraction_status_var.set(status)
        if hasattr(self, 'indicator_status_label'):
            self.indicator_status_label.config(foreground=color)
        # 同时更新网页状态
        if hasattr(self, 'update_web_status'):
            self.update_web_status(f"指标筛选: {status}", color)
        if hasattr(self, 'root'):
            self.root.update_idletasks()
    
    def close_web_automation(self):
        """关闭网页自动化"""
        def close_automation():
            try:
                if self.web_automator:
                    # 在异步循环中关闭浏览器
                    future = asyncio.run_coroutine_threadsafe(
                        self.web_automator.close_browser(),
                        self.web_loop
                    )
                    future.result(timeout=30)  # 30秒超时
                
                # 更新UI
                self.root.after(0, self._on_automation_closed)
                
            except Exception as e:
                error_msg = f"关闭浏览器失败: {str(e)}"
                self.logger.error(error_msg)
                self.root.after(0, self._on_automation_closed, error_msg)
        
        # 更新状态
        self.update_web_status("正在关闭浏览器...", "orange")
        self.close_browser_btn.config(state=tk.DISABLED)
        
        # 在后台线程中运行
        close_thread = threading.Thread(target=close_automation, daemon=True)
        close_thread.start()
    
    def _on_automation_closed(self, error_msg: str = ""):
        """处理浏览器关闭结果"""
        if error_msg:
            self.update_web_status("关闭失败", "red")
            messagebox.showerror("错误", error_msg)
        else:
            self.update_web_status("浏览器已关闭", "green")
        
        # 重置按钮状态
        self.start_browser_btn.config(state=tk.NORMAL)
        self.start_extraction_btn.config(state=tk.DISABLED)
        self.get_indicator_btn.config(state=tk.DISABLED)  # 禁用指标筛选按钮
        self.close_browser_btn.config(state=tk.DISABLED)
        
        # 清理资源
        self.web_automator = None
    
    def open_output_folder(self):
        """打开输出文件夹"""
        try:
            import os
            import subprocess
            import platform
            
            output_dir = "web_automation_results"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 根据操作系统选择打开方式
            if platform.system() == "Windows":
                os.startfile(output_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])
                
        except Exception as e:
            self.logger.error(f"打开输出文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开输出文件夹: {str(e)}")
    
    def cleanup_web_automation(self):
        """清理网页自动化资源"""
        try:
            if self.web_automator:
                # 异步关闭浏览器
                if self.web_loop and not self.web_loop.is_closed():
                    future = asyncio.run_coroutine_threadsafe(
                        self.web_automator.close_browser(),
                        self.web_loop
                    )
                    try:
                        future.result(timeout=5)  # 5秒超时
                    except:
                        pass  # 忽略超时错误
            
            # 停止事件循环
            if self.web_loop and not self.web_loop.is_closed():
                self.web_loop.call_soon_threadsafe(self.web_loop.stop)
                
        except Exception as e:
            self.logger.error(f"清理网页自动化资源时出错: {str(e)}")
    
    def select_analysis_folder(self):
        """选择要分析的文件夹（通过选择文件夹中的Excel文件来确定文件夹）"""
        try:
            # 通过选择Excel文件来确定文件夹路径
            filetypes = [
                ("Excel files", "*.xlsx *.xls"),
                ("Excel 2007+", "*.xlsx"),
                ("Excel 97-2003", "*.xls"),
                ("All files", "*.*")
            ]
            
            filename = filedialog.askopenfilename(
                title="选择文件夹中的任意一个Excel文件（用于确定要分析的文件夹）",
                filetypes=filetypes
            )
            
            if filename:
                # 获取文件所在的文件夹路径
                folder_path = os.path.dirname(filename)
                self.selected_folder_var.set(folder_path)
                self.analyze_stocks_btn.config(state=tk.NORMAL)
                self.logger.info(f"已选择分析文件夹: {folder_path}")
            
        except Exception as e:
            error_msg = f"选择文件夹失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def analyze_same_stocks(self):
        """分析相同股票在多个文件中的出现情况"""
        folder_path = self.selected_folder_var.get()
        if not folder_path:
            messagebox.showwarning("提示", "请先选择要分析的文件夹")
            return
        
        def run_analysis():
            try:
                self.root.after(0, lambda: self.update_web_status("正在分析股票数据...", "orange"))
                self.root.after(0, lambda: self.analyze_stocks_btn.config(state=tk.DISABLED))
                
                # 获取文件夹中的所有Excel文件
                excel_files = []
                for file in os.listdir(folder_path):
                    if file.lower().endswith(('.xlsx', '.xls')):
                        excel_files.append(os.path.join(folder_path, file))
                
                if not excel_files:
                    self.root.after(0, lambda: messagebox.showwarning("提示", "文件夹中没有找到Excel文件"))
                    return
                
                # 股票代码统计字典
                stock_stats = {}  # {股票代码: {'count': 出现次数, 'files': [文件名列表]}}
                
                self.logger.info(f"开始分析 {len(excel_files)} 个Excel文件")
                
                # 逐个读取Excel文件
                for i, file_path in enumerate(excel_files):
                    try:
                        file_name = os.path.basename(file_path)
                        self.root.after(0, lambda fn=file_name, idx=i+1, total=len(excel_files): 
                                       self.update_web_status(f"正在处理文件 {idx}/{total}: {fn}", "orange"))
                        
                        # 读取Excel文件的第一列（股票代码）
                        df = pd.read_excel(file_path, usecols=[0])  # 只读取第一列
                        
                        if df.empty:
                            self.logger.warning(f"文件 {file_name} 为空")
                            continue
                        
                        # 获取第一列的所有值（股票代码）
                        stock_codes = df.iloc[:, 0].dropna().astype(str).unique()
                        
                        # 统计每个股票代码
                        for stock_code in stock_codes:
                            stock_code = str(stock_code).strip()
                            if stock_code and stock_code != "nan":
                                if stock_code not in stock_stats:
                                    stock_stats[stock_code] = {'count': 0, 'files': []}
                                
                                stock_stats[stock_code]['count'] += 1
                                stock_stats[stock_code]['files'].append(file_name)
                        
                        self.logger.info(f"文件 {file_name} 处理完成，发现 {len(stock_codes)} 个唯一股票代码")
                        
                    except Exception as e:
                        self.logger.error(f"处理文件 {file_name} 时出错: {str(e)}")
                        continue
                
                # 整理结果数据
                results = []
                for stock_code, info in stock_stats.items():
                    results.append({
                        '股票代码': stock_code,
                        '出现次数': info['count'],
                        '出现的文件名称': '; '.join(info['files'])
                    })
                
                # 按出现次数降序排列
                results.sort(key=lambda x: x['出现次数'], reverse=True)
                
                # 保存结果到Excel文件
                output_dir = "stock_analysis_results"
                os.makedirs(output_dir, exist_ok=True)
                
                output_file = os.path.join(output_dir, 
                    f"股票重复分析结果_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                
                result_df = pd.DataFrame(results)
                result_df.to_excel(output_file, index=False, sheet_name="股票重复分析")
                
                # 生成分析报告
                total_unique_stocks = len(results)
                max_occurrence = max([r['出现次数'] for r in results]) if results else 0
                stocks_in_multiple_files = len([r for r in results if r['出现次数'] > 1])
                
                # 更新UI并显示结果
                success_msg = (
                    f"股票分析完成！\n\n"
                    f"分析了 {len(excel_files)} 个Excel文件\n"
                    f"发现 {total_unique_stocks} 个唯一股票代码\n"
                    f"其中 {stocks_in_multiple_files} 个股票出现在多个文件中\n"
                    f"最大出现次数: {max_occurrence}\n\n"
                    f"结果已保存到: {output_file}"
                )
                
                self.root.after(0, lambda: self._on_analysis_completed(True, success_msg, output_file))
                
            except Exception as e:
                error_msg = f"股票分析失败: {str(e)}"
                self.logger.error(error_msg)
                self.root.after(0, lambda: self._on_analysis_completed(False, error_msg, None))
        
        # 在后台线程中运行分析
        analysis_thread = threading.Thread(target=run_analysis, daemon=True)
        analysis_thread.start()
    
    def _on_analysis_completed(self, success: bool, message: str, output_file: str = None):
        """处理股票分析完成结果"""
        if success:
            self.update_web_status("股票分析完成", "green")
            messagebox.showinfo("分析成功", message)
            
            # 询问是否打开结果文件
            if output_file and messagebox.askyesno("提示", "是否打开分析结果文件夹？"):
                self.open_analysis_output_folder()
        else:
            self.update_web_status("股票分析失败", "red")
            messagebox.showerror("分析失败", message)
        
        # 重新启用分析按钮
        self.analyze_stocks_btn.config(state=tk.NORMAL)
    
    def open_analysis_output_folder(self):
        """打开股票分析结果文件夹"""
        try:
            import subprocess
            import platform
            
            output_dir = "stock_analysis_results"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 根据操作系统选择打开方式
            if platform.system() == "Windows":
                os.startfile(output_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])
                
        except Exception as e:
            self.logger.error(f"打开分析结果文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开分析结果文件夹: {str(e)}")