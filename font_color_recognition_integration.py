#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体颜色识别集成方案
用于集成到现有的股票信号识别系统中
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Dict, List
import logging

class FontColorRecognizer:
    """字体颜色识别器 - 专门用于集成到现有系统"""
    
    def __init__(self):
        """初始化字体颜色识别器"""
        self.logger = logging.getLogger(__name__)
        
        # 基于实际分析的字体颜色配置
        self.font_color_ranges = {
            '清仓': {
                'rgb_center': [47, 94, 56],    # 绿色系中心点
                'rgb_tolerance': 40,           # RGB容差
                'hsv_range': {                 # HSV范围
                    'h': [60, 80],
                    's': [150, 255], 
                    'v': [80, 150]
                },
                'min_confidence': 0.6
            },
            '持仓': {
                'rgb_center': [138, 82, 3],    # 橙色系中心点
                'rgb_tolerance': 35,
                'hsv_range': {
                    'h': [15, 30],
                    's': [200, 255],
                    'v': [120, 180]
                },
                'min_confidence': 0.6
            },
            '开仓': {
                'rgb_center': [106, 47, 52],   # 红色系中心点
                'rgb_tolerance': 35,
                'hsv_range': {
                    'h': [0, 10],
                    's': [150, 255],
                    'v': [100, 160]
                },
                'min_confidence': 0.6
            }
        }
        
        # 背景过滤参数
        self.bg_black_threshold = 20      # 黑色背景阈值
        self.bg_yellow_min = [200, 180, 0]  # 黄色背景最小值
        self.bg_yellow_max = [255, 255, 50] # 黄色背景最大值
        
        # 最小字体像素数（防止误判）
        self.min_font_pixels = 10
    
    def is_background_pixel(self, rgb: np.ndarray) -> bool:
        """判断是否为背景像素"""
        r, g, b = rgb
        
        # 黑色背景检测
        if r <= self.bg_black_threshold and g <= self.bg_black_threshold and b <= self.bg_black_threshold:
            return True
        
        # 黄色背景检测
        if (r >= self.bg_yellow_min[0] and g >= self.bg_yellow_min[1] and b <= self.bg_yellow_max[2] and
            r <= self.bg_yellow_max[0] and g <= self.bg_yellow_max[1]):
            return True
        
        return False
    
    def extract_font_pixels(self, image: np.ndarray) -> np.ndarray:
        """提取字体像素，过滤背景"""
        # 确保图像是RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            # 如果是BGR格式，转换为RGB
            if image.dtype == np.uint8:
                img_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                img_rgb = image
        else:
            raise ValueError("图像格式不正确")
        
        # 获取所有像素
        pixels = img_rgb.reshape(-1, 3)
        
        # 过滤背景像素
        font_pixels = []
        for pixel in pixels:
            if not self.is_background_pixel(pixel):
                font_pixels.append(pixel)
        
        return np.array(font_pixels) if font_pixels else np.array([]).reshape(0, 3)
    
    def calculate_rgb_confidence(self, avg_color: np.ndarray, target_color: List[int], tolerance: int) -> float:
        """计算RGB颜色匹配置信度"""
        distance = np.sqrt(np.sum((avg_color - np.array(target_color)) ** 2))
        if distance > tolerance:
            return 0.0
        return max(0.0, 1.0 - (distance / tolerance))
    
    def recognize_signal_by_font_color(self, image: np.ndarray) -> Tuple[Optional[str], float, Dict]:
        """
        基于字体颜色识别信号
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            (signal_type, confidence, details)
        """
        try:
            # 提取字体像素
            font_pixels = self.extract_font_pixels(image)
            
            if len(font_pixels) < self.min_font_pixels:
                return None, 0.0, {
                    'error': 'insufficient_font_pixels',
                    'font_pixel_count': len(font_pixels)
                }
            
            # 计算平均字体颜色
            avg_font_color = np.mean(font_pixels, axis=0)
            
            # 与各信号类型匹配
            best_signal = None
            best_confidence = 0.0
            all_confidences = {}
            
            for signal_type, config in self.font_color_ranges.items():
                confidence = self.calculate_rgb_confidence(
                    avg_font_color, 
                    config['rgb_center'], 
                    config['rgb_tolerance']
                )
                
                all_confidences[signal_type] = confidence
                
                if confidence >= config['min_confidence'] and confidence > best_confidence:
                    best_signal = signal_type
                    best_confidence = confidence
            
            return best_signal, best_confidence, {
                'avg_font_color': avg_font_color.tolist(),
                'font_pixel_count': len(font_pixels),
                'all_confidences': all_confidences
            }
            
        except Exception as e:
            self.logger.error(f"字体颜色识别失败: {e}")
            return None, 0.0, {'error': str(e)}

# 集成到现有系统的示例代码
class EnhancedSignalAnalyzer:
    """增强的信号分析器 - 集成字体颜色识别"""
    
    def __init__(self):
        self.font_color_recognizer = FontColorRecognizer()
        self.logger = logging.getLogger(__name__)
        
        # 识别模式配置
        self.recognition_modes = {
            'font_color_only': self._recognize_by_font_color_only,
            'font_color_with_ocr_fallback': self._recognize_with_fallback,
            'hybrid': self._recognize_hybrid
        }
        
        self.current_mode = 'font_color_with_ocr_fallback'
    
    def _recognize_by_font_color_only(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """仅使用字体颜色识别"""
        signal, confidence, details = self.font_color_recognizer.recognize_signal_by_font_color(image)
        return signal, confidence
    
    def _recognize_with_fallback(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """字体颜色识别 + OCR兜底"""
        # 首先尝试字体颜色识别
        signal, confidence, details = self.font_color_recognizer.recognize_signal_by_font_color(image)
        
        if signal and confidence >= 0.6:
            return signal, confidence
        
        # 如果字体颜色识别失败或置信度不足，使用OCR
        self.logger.info(f"字体颜色识别置信度不足({confidence:.3f})，启用OCR兜底")
        return self._fallback_to_ocr(image)
    
    def _recognize_hybrid(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """混合识别：字体颜色 + OCR验证"""
        # 字体颜色识别
        signal, confidence, details = self.font_color_recognizer.recognize_signal_by_font_color(image)
        
        if signal and confidence >= 0.8:
            # 高置信度直接返回
            return signal, confidence
        elif signal and confidence >= 0.4:
            # 中等置信度，OCR验证
            ocr_signal, ocr_confidence = self._fallback_to_ocr(image)
            if ocr_signal == signal:
                # OCR确认，提升置信度
                return signal, min(1.0, confidence + 0.3)
            else:
                # OCR不一致，降低置信度
                return signal, confidence * 0.7
        else:
            # 低置信度，使用OCR
            return self._fallback_to_ocr(image)
    
    def _fallback_to_ocr(self, image: np.ndarray) -> Tuple[Optional[str], float]:
        """OCR兜底识别（需要集成现有OCR代码）"""
        # 这里应该调用现有的OCR识别方法
        # 示例代码：
        # return self.existing_ocr_recognizer.recognize(image)
        
        # 临时返回，实际使用时需要替换
        self.logger.warning("OCR兜底方法需要实现")
        return None, 0.0
    
    def recognize_signal(self, image: np.ndarray, mode: str = None) -> Dict:
        """
        识别信号的主入口
        
        Args:
            image: 输入图像
            mode: 识别模式，如果为None则使用默认模式
            
        Returns:
            识别结果字典
        """
        if mode is None:
            mode = self.current_mode
        
        if mode not in self.recognition_modes:
            raise ValueError(f"不支持的识别模式: {mode}")
        
        try:
            signal, confidence = self.recognition_modes[mode](image)
            
            return {
                'success': signal is not None,
                'signal': signal,
                'confidence': confidence,
                'mode': mode
            }
            
        except Exception as e:
            self.logger.error(f"信号识别失败: {e}")
            return {
                'success': False,
                'signal': None,
                'confidence': 0.0,
                'mode': mode,
                'error': str(e)
            }

# 使用示例
def integration_example():
    """集成使用示例"""
    
    # 创建增强的信号分析器
    analyzer = EnhancedSignalAnalyzer()
    
    # 测试图片路径
    test_images = [
        'screenshots/qc2.png',  # 清仓
        'screenshots/cc2.png',  # 持仓  
        'screenshots/kc2.png'   # 开仓
    ]
    
    print("=== 字体颜色识别集成测试 ===\n")
    
    for image_path in test_images:
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图片: {image_path}")
            continue
        
        # 识别信号
        result = analyzer.recognize_signal(image, mode='font_color_with_ocr_fallback')
        
        print(f"图片: {image_path}")
        if result['success']:
            print(f"  识别结果: {result['signal']}")
            print(f"  置信度: {result['confidence']:.3f}")
            print(f"  模式: {result['mode']}")
        else:
            print(f"  识别失败: {result.get('error', 'unknown')}")
        print()

if __name__ == "__main__":
    integration_example()
