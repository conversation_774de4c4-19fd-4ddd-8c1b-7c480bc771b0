# 股票筛选器操作手册

## 目录
- [1. 软件概述](#1-软件概述)
- [2. 系统要求](#2-系统要求)  
- [3. 安装配置](#3-安装配置)
- [4. 界面介绍](#4-界面介绍)
- [5. 主要功能使用教程](#5-主要功能使用教程)
- [6. 常见问题](#6-常见问题)
- [7. 故障排除](#7-故障排除)

---

## 1. 软件概述

### 1.1 软件简介

股票筛选器是一款专业的股票资金流数据分析工具，主要功能包括：

- **指南针数据分析**：通过OCR技术自动从指南针软件中提取股票多空资金流数据
- **网页数据获取**：从各大财经网站自动获取股票相关数据
- **实时信号监控**：监控买入和卖出信号，支持实时通知
- **批量股票分析**：支持批量处理多只股票的数据分析
- **智能筛选**：根据可配置的筛选条件自动筛选出符合要求的股票

### 1.2 核心特性

- **🔍 OCR识别技术**：基于PaddleOCR等先进技术，支持多种图像缩放模式
- **🤖 自动化操作**：完全自动化的数据提取和分析流程  
- **📊 多数据源**：支持指南针软件和网页数据双重获取
- **⚡ 实时监控**：支持买入/卖出信号的实时监控和通知
- **🛠️ 灵活配置**：支持OCR区域、鼠标位置等多种配置选项
- **📱 钉钉集成**：支持钉钉通知功能

### 1.3 适用场景

- 股票投资分析师进行批量数据分析
- 个人投资者进行股票筛选和信号监控
- 金融机构进行市场数据收集和分析
- 量化交易策略的数据支撑

---

## 2. 系统要求

### 2.1 硬件要求

- **操作系统**：Windows 10/11 (64位)
- **内存**：最低4GB，推荐8GB以上
- **存储空间**：至少2GB可用空间
- **显示器**：支持多显示器配置
- **网络**：稳定的互联网连接

### 2.2 软件依赖

#### 必需软件
- **Python 3.8+**：运行环境
- **指南针金融软件**：数据源软件（仅指南针数据分析功能需要）
- **Microsoft Excel**：数据处理和导出

#### 可选软件  
- **CUDA**：GPU加速OCR性能（推荐但非必需）

### 2.3 Python依赖包

以下依赖包会在安装过程中自动安装：

**核心功能包：**
- `paddleocr>=3.0` - OCR文字识别
- `pywinauto>=0.6.8` - Windows自动化
- `pandas>=2.0.0` - 数据处理
- `openpyxl>=3.1.0` - Excel文件处理

**辅助功能包：**
- `playwright>=1.40.0` - 网页自动化
- `requests>=2.31.0` - HTTP请求
- `keyboard>=0.13.5` - 全局快捷键
- `mss>=9.0.1` - 屏幕截图

**图像处理包：**
- `opencv-python>=4.8.0` - 图像处理
- `Pillow>=10.0.0` - 图像处理
- `numpy>=1.24.0` - 数值计算

### 2.4 系统权限

软件运行需要以下系统权限：
- **屏幕截图权限**：用于OCR数据提取
- **鼠标和键盘控制权限**：用于自动化操作
- **文件读写权限**：用于配置和数据文件操作
- **网络访问权限**：用于网页数据获取和钉钉通知

---

## 3. 安装配置

### 3.1 快速安装（推荐）

1. **下载软件包**
   - 从发布页面下载最新版本的软件包
   - 解压到任意目录（建议路径不包含中文和空格）

2. **运行安装脚本**
   ```bash
   # 双击运行 deploy.bat 进行一键部署
   deploy.bat
   ```
   
   或者使用智能启动脚本：
   ```bash
   # 双击运行 start.bat 进行智能启动
   start.bat
   ```

3. **安装验证**
   - 安装脚本会自动检测Python环境
   - 自动安装所有依赖包
   - 创建桌面快捷方式
   - 进行权限检查

### 3.2 手动安装

如果自动安装遇到问题，可以采用手动安装：

1. **安装Python环境**
   ```bash
   # 下载并安装 Python 3.8 或更高版本
   # 确保勾选 "Add Python to PATH" 选项
   ```

2. **安装依赖包**
   ```bash
   # 进入软件目录，打开命令行
   pip install -r requirements.txt
   
   # 或者使用智能安装器
   python install_dependencies.py
   ```

3. **安装Playwright浏览器**（网页自动化功能需要）
   ```bash
   playwright install chromium
   ```

4. **验证安装**
   ```bash
   # 运行主程序验证安装
   python main.py
   ```

### 3.3 指南针软件配置

1. **安装指南针软件**
   - 从官方渠道下载并安装指南针金融软件
   - 确保软件能正常启动和登录

2. **配置软件路径**（可选）
   - 打开 `config.py` 文件
   - 修改 `COMPASS_SOFTWARE['exe_path']` 为实际安装路径
   - 注意：键盘操作模式下可手动启动，无需配置路径

3. **验证指南针集成**
   - 启动指南针软件
   - 运行股票筛选器
   - 检查软件是否能检测到指南针进程

### 3.4 首次配置

1. **OCR区域设置**
   - 启动软件后，点击"选择OCR区域"
   - 在指南针软件中框选资金流数据显示区域
   - 保存区域配置

2. **鼠标基准位置设置**
   - 点击"设置鼠标基准位置"
   - 将鼠标移动到指南针软件的合适位置
   - 按下Ctrl键保存位置

3. **信号区域设置**
   - 设置买入信号检测区域
   - 设置状态信息检测区域
   - 调整OCR图像缩放模式（原图/2倍图/4倍图）

---

## 4. 界面介绍

### 4.1 主界面布局

软件采用标签页设计，主要包含两个功能标签页：

#### 【1】指南针数据分析 标签页

**顶部区域：文件路径**
- Excel文件路径输入框：显示当前选择的股票列表文件
- 浏览按钮：选择包含股票代码的Excel文件

**左侧功能区域：**

1. **信号监控区域**
   - 信号监控按钮：买入/停止、卖出/停止
   - 信号测试按钮：基础测试、增强测试

2. **股票分析区域**
   - 开始分析按钮：启动批量股票分析
   - 暂停/继续按钮：控制分析流程
   - 进度条：显示分析进度

3. **OCR配置区域**
   - 选择OCR区域：设置资金流数据识别区域
   - 图像缩放选择：原图/2倍图/4倍图
   - 测试OCR按钮：验证OCR配置效果

4. **鼠标操作区域**
   - 设置基准位置：设置鼠标操作的基准坐标
   - 启用鼠标定位：开启/关闭鼠标定位功能

5. **高级功能区域**
   - 股票自动输入：批量自动输入股票代码
   - 指标抓取：抓取小草指标数据
   - 截图功能：屏幕截图保存

**右侧区域：运行日志**
- 实时显示软件运行状态和日志信息
- 支持滚动查看历史日志

#### 【2】网页数据获取 标签页

- 网页自动化配置
- 数据源选择和配置
- 批量网页数据抓取
- 数据验证和导出

**底部状态栏**
- 显示当前软件运行状态
- 实时更新操作进度信息

### 4.2 快捷键支持

- **Ctrl+Alt+S**：全局暂停快捷键
  - 暂停当前股票分析
  - 停止信号监控
  - 中断正在进行的自动化操作

### 4.3 窗口特性

- **自适应布局**：支持窗口大小调整
- **最小尺寸限制**：确保界面元素正常显示
- **居中显示**：启动时自动居中显示
- **版本信息显示**：窗口标题显示当前软件版本

---

## 5. 主要功能使用教程

### 5.1 指南针数据分析功能

#### 5.1.1 准备工作

1. **启动指南针软件**
   - 手动启动指南针金融软件
   - 登录您的账户
   - 确保软件界面正常显示

2. **准备股票列表Excel文件**
   - 创建或准备包含股票代码的Excel文件
   - 股票代码应放在第一列
   - 支持6位数字代码（如：000001）和8位字母前缀代码（如：SH000001）

#### 5.1.2 OCR区域配置

1. **选择OCR区域**
   - 在指南针软件中打开任意一只股票的资金流数据页面
   - 在股票筛选器中点击"选择OCR区域"按钮
   - 用鼠标框选资金流数据显示的区域
   - 确保框选区域完整包含需要识别的数字

2. **调整图像缩放模式**
   - 根据文字大小选择合适的缩放模式：
     - **原图**：适用于文字清晰、较大的情况
     - **2倍图**：适用于文字较小的情况  
     - **4倍图**：适用于文字极小或模糊的情况

3. **测试OCR效果**
   - 点击"测试OCR"按钮验证识别效果
   - 查看日志区域的识别结果
   - 如识别不准确，重新调整区域或缩放模式

#### 5.1.3 鼠标基准位置设置

1. **设置基准位置**
   - 将鼠标移动到指南针软件中的合适位置
   - 建议选择股票搜索框或其他稳定的界面元素附近
   - 点击"设置鼠标基准位置"按钮
   - 按下Ctrl键保存当前鼠标位置

2. **启用鼠标定位**
   - 勾选"启用鼠标定位"选项
   - 这样在每只股票分析前会自动移动鼠标到基准位置

#### 5.1.4 执行批量分析

1. **选择股票列表文件**
   - 点击"浏览"按钮选择包含股票代码的Excel文件
   - 确认文件路径正确显示

2. **开始分析**
   - 点击"开始分析"按钮启动批量分析
   - 软件将自动：
     - 逐个输入股票代码到指南针软件
     - 等待页面加载完成
     - 截图并进行OCR识别
     - 提取资金流数据
     - 根据筛选条件过滤股票

3. **监控分析过程**
   - 观察进度条了解分析进度
   - 查看运行日志了解详细过程
   - 可随时点击"暂停"按钮或按Ctrl+Alt+S暂停分析

4. **查看结果**
   - 分析完成后结果会自动保存为Excel文件
   - 文件名包含时间戳，便于区分
   - 结果包含符合筛选条件的股票及其资金流数据

### 5.2 信号监控功能

#### 5.2.1 买入信号监控

1. **配置买入信号区域**
   - 点击"选择买入信号区域"按钮
   - 在指南针软件中框选买入信号显示区域
   - 保存区域配置

2. **启动监控**
   - 点击"买入"按钮启动买入信号监控
   - 软件将持续监控指定区域的信号变化
   - 检测到买入信号时会：
     - 在日志中记录
     - 发送钉钉通知（如已配置）
     - 保存截图证据

3. **停止监控**
   - 点击"停止"按钮或按Ctrl+Alt+S停止监控

#### 5.2.2 卖出信号监控

操作步骤与买入信号监控类似，只需：
1. 配置卖出信号检测区域
2. 点击"卖出"按钮启动监控
3. 根据需要停止监控

#### 5.2.3 信号测试功能

- **基础测试**：测试基本的信号识别功能
- **增强测试**：测试增强的信号识别功能，包括橙色信号分类器

### 5.3 网页数据获取功能

#### 5.3.1 配置网页自动化

1. **切换到网页数据获取标签页**

2. **配置网站登录信息**
   - 点击"配置登录信息"
   - 输入目标网站的登录凭据
   - 信息将加密保存

3. **配置数据提取元素**
   - 使用网页元素配置向导
   - 设置需要提取的数据元素选择器
   - 保存配置

#### 5.3.2 执行网页数据抓取

1. **选择数据源网站**
   - 从预配置的网站列表中选择
   - 或添加新的数据源

2. **启动自动化抓取**
   - 点击"开始抓取"按钮
   - 软件将自动：
     - 打开浏览器
     - 登录目标网站
     - 导航到数据页面
     - 提取所需数据
     - 保存到Excel文件

3. **监控抓取过程**
   - 观察浏览器自动化操作
   - 查看日志了解抓取进度
   - 处理可能的验证码或异常

### 5.4 高级功能

#### 5.4.1 股票自动输入

1. **准备股票代码列表**
   - 选择包含股票代码的Excel文件

2. **配置输入参数**
   - 设置输入间隔时间
   - 配置错误处理策略

3. **执行自动输入**
   - 点击"开始输入"按钮
   - 软件将逐个自动输入股票代码到指南针软件

#### 5.4.2 小草指标抓取

1. **选择指标类型**
   - 从下拉菜单选择要抓取的小草指标

2. **启动指标抓取**
   - 点击"开始抓取"按钮
   - 软件将自动抓取指定指标数据

#### 5.4.3 截图功能

1. **配置截图区域**
   - 可使用OCR区域或自定义区域

2. **执行截图**
   - 点击"截图"按钮
   - 截图将保存到screenshots目录

---

## 6. 常见问题

### 6.1 安装相关问题

**Q: 运行start.bat提示找不到Python？**
A: 
1. 确认已正确安装Python 3.8+
2. 检查Python是否已添加到系统PATH环境变量
3. 尝试重新安装Python并勾选"Add Python to PATH"选项

**Q: 依赖包安装失败？**
A: 
1. 检查网络连接是否正常
2. 尝试使用国内镜像源：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`
3. 使用智能安装器：`python install_dependencies.py`

**Q: PaddleOCR模型下载慢或失败？**
A: 
1. 首次运行会自动下载OCR模型，请耐心等待
2. 检查网络连接
3. 如下载失败，删除models目录后重新运行

### 6.2 指南针集成问题

**Q: 软件检测不到指南针软件？**
A: 
1. 确认指南针软件已启动并正常运行
2. 检查指南针软件进程名是否为"WavMain.exe"
3. 确认指南针软件窗口标题包含"指南针"关键词

**Q: OCR识别不准确？**
A: 
1. 重新选择OCR区域，确保完整包含数据区域
2. 尝试不同的图像缩放模式（原图/2倍图/4倍图）
3. 确保指南针软件界面清晰，避免被其他窗口遮挡
4. 检查显示器缩放设置，建议使用100%缩放

**Q: 鼠标操作不准确？**
A: 
1. 重新设置鼠标基准位置
2. 确保指南针软件窗口位置没有变化
3. 检查是否启用了鼠标定位功能

### 6.3 功能使用问题

**Q: 股票代码无法识别？**
A: 
1. 确认Excel文件中股票代码格式正确
2. 支持6位数字代码（000001）和8位字母前缀代码（SH000001）
3. 检查代码是否在第一列

**Q: 分析过程中断？**
A: 
1. 检查指南针软件是否正常运行
2. 确认网络连接稳定
3. 查看运行日志了解具体错误信息
4. 可使用"继续分析"功能从中断位置恢复

**Q: 信号监控没有反应？**
A: 
1. 重新配置信号检测区域
2. 使用信号测试功能验证配置
3. 检查指南针软件中是否有信号变化
4. 确认监控功能已正确启动

### 6.4 性能优化问题

**Q: OCR识别速度慢？**
A: 
1. 如有NVIDIA显卡，安装CUDA以启用GPU加速
2. 使用简化的PaddleOCR模式而非复杂多引擎模式
3. 适当减小OCR区域大小
4. 关闭不必要的后台程序

**Q: 内存占用过高？**
A: 
1. 定期重启软件释放内存
2. 减少同时处理的股票数量
3. 关闭OCR调试模式
4. 清理临时文件和截图

---

## 7. 故障排除

### 7.1 诊断工具

#### 7.1.1 日志分析
- **位置**：运行日志区域实时显示
- **级别**：可在config.py中调整日志级别
- **用途**：了解详细的运行过程和错误信息

#### 7.1.2 测试功能
- **OCR测试**：验证OCR配置和识别效果
- **信号测试**：测试信号识别功能
- **完整功能测试**：运行`test_complete_functionality.py`

#### 7.1.3 调试模式
```python
# 在config.py中启用调试模式
APP_CONFIG['debug_mode'] = True
```
启用后会保存详细的调试图像和日志。

### 7.2 常见故障及解决方案

#### 7.2.1 OCR相关故障

**故障现象**：OCR识别结果为空或乱码
**解决步骤**：
1. 检查OCR区域是否正确框选
2. 确认区域内有清晰可见的文字
3. 尝试不同的图像缩放模式
4. 检查屏幕显示设置
5. 重新启动软件重新初始化OCR引擎

**故障现象**：OCR引擎初始化失败
**解决步骤**：
1. 检查Python环境和依赖包是否正确安装
2. 删除models目录，重新下载OCR模型
3. 检查系统内存是否充足
4. 尝试切换到简化OCR模式

#### 7.2.2 自动化操作故障

**故障现象**：无法控制指南针软件
**解决步骤**：
1. 确认指南针软件正常运行
2. 检查软件是否以管理员权限运行
3. 确认pywinauto依赖包正确安装
4. 重新启动两个软件

**故障现象**：鼠标键盘操作异常
**解决步骤**：
1. 检查系统权限设置
2. 关闭可能干扰的安全软件
3. 确认没有其他程序占用输入设备
4. 重新设置鼠标基准位置

#### 7.2.3 数据处理故障

**故障现象**：Excel文件读取失败
**解决步骤**：
1. 检查文件路径是否正确
2. 确认文件未被其他程序占用
3. 验证Excel文件格式和内容
4. 尝试另存为新的Excel文件

**故障现象**：数据筛选结果异常
**解决步骤**：
1. 检查筛选条件配置
2. 验证源数据的格式和内容
3. 查看处理日志了解详细过程
4. 尝试手动验证部分数据

### 7.3 系统级故障排除

#### 7.3.1 权限问题
- **以管理员身份运行**：右键软件图标选择"以管理员身份运行"
- **防火墙设置**：将软件添加到防火墙例外列表
- **杀毒软件**：将软件目录添加到杀毒软件白名单

#### 7.3.2 环境问题
- **Python环境**：确保Python版本正确，环境变量设置正确
- **依赖冲突**：使用虚拟环境避免包版本冲突
- **系统兼容性**：确认操作系统版本符合要求

#### 7.3.3 硬件问题
- **内存不足**：关闭不必要程序，增加虚拟内存
- **显卡驱动**：更新显卡驱动以支持CUDA加速
- **显示器设置**：调整显示器缩放和分辨率设置

### 7.4 获取帮助

#### 7.4.1 自助诊断
1. 查看运行日志获取错误详情
2. 运行相关测试脚本验证功能
3. 检查配置文件设置
4. 参考本手册相关章节

#### 7.4.2 问题反馈
提交问题时请提供：
1. 详细的错误描述和重现步骤
2. 软件版本信息
3. 运行日志截图
4. 系统环境信息
5. 配置文件内容（去除敏感信息）

#### 7.4.3 版本更新
- 定期检查软件更新
- 阅读版本更新说明
- 备份重要配置文件
- 按照更新指南操作

---

## 附录

### A. 快捷键列表
- `Ctrl+Alt+S`：全局暂停/停止快捷键

### B. 文件结构说明
```
stock_screener/
├── main.py                    # 程序入口
├── gui_main.py               # 主GUI界面
├── config.py                 # 配置文件
├── requirements.txt          # 依赖包列表
├── start.bat                 # 启动脚本
├── deploy.bat               # 部署脚本
├── screenshots/             # 截图保存目录
├── models/                  # OCR模型目录
└── 各种功能模块文件...
```

### C. 配置文件参数说明
详细的配置参数说明请参考`config.py`文件中的注释。

### D. 版本历史
- **v2025.08.20.2100**：当前版本，增强橙色信号分类器
- **v2025.08.20.1400**：OCR集成策略优化
- **v2025.08.18.2231**：图像缩放处理功能

---

*本手册基于软件版本 v2025.08.20.2100 编写，如有疑问请参考软件内置帮助或联系技术支持。*