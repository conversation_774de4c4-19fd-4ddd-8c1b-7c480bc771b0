# -*- coding: utf-8 -*-
"""
GUI文件操作模块
包含文件选择、保存等相关功能
"""

import os
import pandas as pd
from tkinter import filedialog, messagebox


class GUIFileOperationsMixin:
    """GUI文件操作Mixin类"""
    
    def select_excel_file(self):
        """选择Excel文件"""
        filetypes = [
            ("Excel files", "*.xlsx *.xls"),
            ("Excel 2007+", "*.xlsx"),
            ("Excel 97-2003", "*.xls"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=filetypes
        )
        
        if filename:
            self.excel_path_var.set(filename)
            self.current_excel_path = filename
            self.logger.info(f"已选择Excel文件: {filename}")
    
    def save_results(self):
        """保存结果"""
        # 检查是否有完整的分析结果
        if hasattr(self, 'all_analysis_results') and self.all_analysis_results:
            # 有完整的分析结果，保存所有结果（包括失败的）
            self._save_all_results()
        elif self.data_processor.filtered_stocks:
            # 只有筛选后的结果，使用旧的保存方式
            self._save_filtered_results()
        else:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
    
    def _save_all_results(self):
        """保存所有分析结果（包括成功和失败的）"""
        filetypes = [
            ("Excel files", "*.xlsx"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.asksaveasfilename(
            title="保存完整分析结果（包括失败的股票）",
            defaultextension=".xlsx",
            filetypes=filetypes
        )
        
        if filename:
            try:
                success = self.data_processor.save_all_results_to_excel(filename, self.all_analysis_results)
                if success:
                    # 统计结果
                    total_count = len(self.all_analysis_results)
                    success_count = len([r for r in self.all_analysis_results if r.get('status') == '分析成功'])
                    failed_count = total_count - success_count
                    filtered_count = len([r for r in self.all_analysis_results 
                                        if (r.get('status') == '分析成功' and 
                                            r.get('today_fund', 0) > r.get('yesterday_fund', 0) > r.get('day_before_yesterday_fund', 0))])
                    
                    info_msg = f"""保存成功！
                    
文件位置: {filename}

分析统计:
• 总计股票: {total_count} 只
• 分析成功: {success_count} 只
• 分析失败: {failed_count} 只
• 符合筛选条件: {filtered_count} 只

说明: 此文件包含所有股票的完整分析结果"""
                    
                    messagebox.showinfo("保存成功", info_msg)
                    self.logger.info(f"完整分析结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存失败")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
                self.logger.error(f"保存失败: {str(e)}")
    
    def _save_filtered_results(self):
        """保存筛选后的结果（仅符合条件的股票）"""
        filetypes = [
            ("Excel files", "*.xlsx"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.asksaveasfilename(
            title="保存筛选结果（仅符合条件的股票）",
            defaultextension=".xlsx",
            filetypes=filetypes
        )
        
        if filename:
            try:
                success = self.data_processor.save_results_to_excel(filename, self.data_processor.filtered_stocks)
                if success:
                    messagebox.showinfo("成功", f"结果已保存到: {filename}")
                    self.logger.info(f"结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存失败")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
                self.logger.error(f"保存失败: {str(e)}")
    
    def split_excel_file(self):
        """分拆Excel文件为多个文件（数量可配置）"""
        # 检查是否已选择文件
        if not hasattr(self, 'current_excel_path') or not self.current_excel_path:
            messagebox.showwarning("警告", "请先选择Excel文件")
            return
        
        if not os.path.exists(self.current_excel_path):
            messagebox.showerror("错误", "选择的文件不存在")
            return
            
        try:
            self.logger.info(f"开始分拆Excel文件: {self.current_excel_path}")
            
            # 读取Excel文件
            file_ext = os.path.splitext(self.current_excel_path)[1].lower()
            if file_ext == '.xlsx':
                df = pd.read_excel(self.current_excel_path, engine='openpyxl')
            else:
                df = pd.read_excel(self.current_excel_path, engine='xlrd')
            
            # 检查数据是否为空
            if df.empty:
                messagebox.showerror("错误", "Excel文件为空")
                return
            
            # 获取第一列的股票代码数据
            first_column = df.iloc[:, 0]
            stock_codes = first_column.dropna().astype(str).unique().tolist()
            
            # 规范化股票代码
            normalized_codes = []
            for code in stock_codes:
                code = str(code).strip()
                # 使用data_processor的规范化方法
                if hasattr(self, 'data_processor') and hasattr(self.data_processor, '_normalize_stock_code'):
                    normalized_code = self.data_processor._normalize_stock_code(code)
                else:
                    # 如果没有data_processor，使用简化的规范化逻辑
                    normalized_code = self._simple_normalize_code(code)
                normalized_codes.append(normalized_code)
            
            if not normalized_codes:
                messagebox.showerror("错误", "未找到有效的股票代码")
                return
            
            # 计算分拆参数
            from config import APP_CONFIG
            split_count = APP_CONFIG['excel_split_count']
            
            total_count = len(normalized_codes)
            codes_per_file = total_count // split_count
            remainder = total_count % split_count
            
            # 生成基础文件名
            base_path = os.path.splitext(self.current_excel_path)[0]
            file_ext = os.path.splitext(self.current_excel_path)[1]
            
            saved_files = []
            
            # 分拆成指定数量的文件
            for part_num in range(1, split_count + 1):
                # 计算当前文件的数据范围
                start_idx = (part_num - 1) * codes_per_file
                if part_num == split_count:  # 最后一个文件包含余数
                    end_idx = total_count
                else:
                    end_idx = start_idx + codes_per_file
                
                # 获取当前部分的股票代码
                part_codes = normalized_codes[start_idx:end_idx]
                
                # 创建DataFrame，第一行是表头
                part_df = pd.DataFrame({'股票代码': part_codes})
                
                # 生成文件名
                part_filename = f"{base_path}_part{part_num}{file_ext}"
                
                # 保存文件
                part_df.to_excel(part_filename, index=False, engine='openpyxl')
                saved_files.append(part_filename)
                
                self.logger.info(f"已生成第{part_num}个文件: {part_filename} (包含{len(part_codes)}个股票代码)")
            
            # 显示成功消息
            success_msg = f"文件分拆完成！\n共生成{split_count}个文件，总计{total_count}个股票代码：\n"
            for i, filename in enumerate(saved_files, 1):
                basename = os.path.basename(filename)
                success_msg += f"文件{i}: {basename}\n"
            
            messagebox.showinfo("成功", success_msg)
            self.logger.info(f"Excel文件分拆完成，共生成{len(saved_files)}个文件")
            
        except Exception as e:
            error_msg = f"分拆文件失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.logger.error(error_msg)
    
    def _simple_normalize_code(self, code: str) -> str:
        """简化版股票代码规范化（备用方法）"""
        code = str(code).strip()
        
        # 处理8位字母前缀格式
        if len(code) == 8 and code[:2].isalpha() and code[2:].isdigit():
            return code[2:]  # 取后6位
        
        # 处理4位数补零
        if code.isdigit() and len(code) == 4:
            return '00' + code
            
        return code