# 信号筛选功能使用说明

## 功能概述

新增的"信号筛选"功能模块可以基于用户选择的Excel文件中的股票列表，实时监控每只股票的交易信号，当信号值匹配用户设定的目标值时，自动筛选并保存符合条件的股票。

## 界面位置

在主界面的左侧区域，"信号监控"下方新增了"信号筛选"功能区域，包含：

- **筛选信号** 按钮：启动信号筛选功能
- **停止筛选** 按钮：停止当前筛选任务
- **目标信号** 下拉框：选择要筛选的信号类型
  - 开仓：筛选出当前信号为"开仓"的股票
  - 清仓：筛选出当前信号为"清仓"的股票
  - 持仓：筛选出当前信号为"持仓"的股票（默认选项）

## 使用步骤

### 1. 准备工作
1. 确保已选择Excel文件（在"文件路径"中选择包含股票代码的Excel文件）
2. 确保已配置信号区域（点击"选择信号区域"按钮配置信号识别区域）
3. 确保OCR引擎已正常初始化

### 2. 设置筛选条件
1. 在"目标信号"下拉框中选择要筛选的信号类型：
   - **开仓**：寻找发出开仓信号的股票
   - **清仓**：寻找发出清仓信号的股票  
   - **持仓**：寻找当前为持仓状态的股票

### 3. 开始筛选
1. 点击"筛选信号"按钮启动筛选
2. 系统会自动读取Excel文件中的股票代码
3. 逐个检查每只股票的当前信号
4. 实时显示筛选进度和结果

### 4. 查看结果
筛选完成后，系统会：
1. 显示筛选完成的统计信息
2. 自动保存结果到 `analysis_results/` 目录
3. 生成两个Excel文件：
   - `信号筛选结果_符合_[信号类型]_[时间戳].xlsx`：符合条件的股票
   - `信号筛选结果_不符合_[信号类型]_[时间戳].xlsx`：不符合条件的股票

## 结果文件格式

### 符合条件的股票文件
包含以下列：
- **股票代码**：股票代码
- **匹配信号**：匹配的信号类型
- **匹配时间**：信号匹配的时间
- **状态**：固定为"符合条件"

### 不符合条件的股票文件
包含以下列：
- **股票代码**：股票代码
- **不匹配原因**：不匹配的具体原因
- **检查时间**：信号检查的时间
- **状态**：固定为"不符合条件"

## 技术特点

### 1. 简化流程
- **移除热身步骤**：与买卖信号监控不同，信号筛选直接进行信号检测，无需"第一轮热身"
- **一次性筛选**：不是持续监控，而是对所有股票进行一次完整的信号检测

### 2. 独立运行
- **与现有功能互不干扰**：可以与买卖信号监控功能同时运行
- **独立的监控器**：使用专门的`SignalFilteringMonitor`类
- **独立的结果保存**：结果保存到专用文件，不影响其他功能

### 3. 复用现有代码
- **信号识别逻辑**：完全复用现有的`enhanced_signal_analyzer`
- **页面操作逻辑**：复用`compass_automator`的股票搜索和页面切换功能
- **配置系统**：使用相同的信号配置和OCR配置

## 注意事项

1. **前置条件检查**：
   - 必须先选择包含股票代码的Excel文件
   - 必须先配置信号识别区域
   - OCR引擎必须正常初始化

2. **Excel文件格式**：
   - 支持 `.xlsx` 和 `.xls` 格式
   - 股票代码应在第一列
   - 支持6位数字格式（如：000001、600519）
   - 自动处理4位数补零（如：1320 → 001320）

3. **运行环境**：
   - 需要指南针软件正常运行
   - 确保信号区域配置正确
   - 建议在交易时间内使用以获得准确信号

4. **性能考虑**：
   - 筛选速度取决于股票数量和页面加载速度
   - 建议单次筛选股票数量不超过100只
   - 可随时点击"停止筛选"中断操作

## 故障排除

### 常见问题
1. **"请先选择Excel文件"**：需要在文件路径中选择包含股票代码的Excel文件
2. **"请先选择信号区域"**：需要点击"选择信号区域"按钮配置信号识别区域
3. **"OCR引擎未初始化"**：重启程序或检查OCR配置
4. **"未找到有效的股票代码"**：检查Excel文件格式，确保第一列包含有效的股票代码

### 日志查看
- 筛选过程中的详细信息会显示在右侧的"运行日志"区域
- 可以通过日志了解筛选进度和任何错误信息

## 更新日志

**版本**: 2025-09-07
- ✅ 新增信号筛选功能模块
- ✅ 支持开仓、清仓、持仓三种信号类型筛选
- ✅ 独立的GUI控件和操作流程
- ✅ 自动保存筛选结果到Excel文件
- ✅ 与现有买卖信号监控功能完全兼容
