# -*- coding: utf-8 -*-
"""
多空资金OCR验证功能模块
用于测试不同图像处理方式对OCR识别效果的影响，帮助找到容错率高的方案
"""

import cv2
import numpy as np
import logging
import time
import os
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

from image_processor import ImageProcessor
from simple_paddleocr_engine import SimplePaddleOCREngine


class FundOCRValidator:
    """多空资金OCR验证器"""
    
    def __init__(self, debug_mode: bool = True):
        """
        初始化OCR验证器
        
        Args:
            debug_mode: 是否启用调试模式
        """
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        
        # 初始化图像处理器
        self.image_processor = ImageProcessor(debug_mode=debug_mode, save_debug_images=True)
        
        # 初始化OCR引擎
        self.ocr_engine = SimplePaddleOCREngine(use_gpu=True, debug_mode=debug_mode)
        
        # 创建统一的验证结果保存目录
        self.base_validation_dir = "fund_ocr_validation"
        os.makedirs(self.base_validation_dir, exist_ok=True)
        
        # 为本次验证创建时间戳标识
        self.current_test_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        self.logger.info(f"多空资金OCR验证器初始化完成，结果保存目录: {self.base_validation_dir}")
    
    def validate_fund_ocr(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        验证多空资金OCR识别效果
        使用四种不同的图像处理方式进行测试
        
        Args:
            x, y: OCR区域左上角坐标
            width, height: OCR区域宽高
            
        Returns:
            验证结果字典
        """
        self.logger.info(f"开始多空资金OCR验证，区域: ({x}, {y}, {width}, {height})")
        
        # 验证结果存储
        validation_results = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'region': {'x': x, 'y': y, 'width': width, 'height': height},
            'test_methods': {},
            'summary': {},
            'recommendations': []
        }
        
        try:
            # 首先截取原始图像
            original_image = self.image_processor.capture_region(x, y, width, height)
            if original_image is None:
                return {
                    'success': False,
                    'error': '无法截取原始图像',
                    'validation_results': validation_results
                }
            
            # 保存原始图像
            original_path = os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_original.png')
            cv2.imwrite(original_path, original_image)
            self.logger.info(f"原始图像已保存: {original_path}")
            
            # 测试方法1: 原图
            self.logger.info("测试方法1: 使用原图进行OCR识别")
            method1_result = self._test_original_image(original_image)
            validation_results['test_methods']['method1_original'] = method1_result
            
            # 测试方法2: 图片*2
            self.logger.info("测试方法2: 使用2倍放大图片进行OCR识别")
            method2_result = self._test_scaled_image(original_image, scale_factor=2, method_name="2x_scaled")
            validation_results['test_methods']['method2_2x_scaled'] = method2_result
            
            # 测试方法3: 图片*4
            self.logger.info("测试方法3: 使用4倍放大图片进行OCR识别")
            method3_result = self._test_scaled_image(original_image, scale_factor=4, method_name="4x_scaled")
            validation_results['test_methods']['method3_4x_scaled'] = method3_result
            
            # 测试方法4: 设置DPI为300
            self.logger.info("测试方法4: 使用DPI300处理图片进行OCR识别")
            method4_result = self._test_dpi_enhanced_image(original_image)
            validation_results['test_methods']['method4_dpi300'] = method4_result
            
            # 生成总结和建议
            validation_results['summary'] = self._generate_summary(validation_results['test_methods'])
            validation_results['recommendations'] = self._generate_recommendations(validation_results['test_methods'])
            
            # 保存验证报告
            self._save_validation_report(validation_results)
            
            return {
                'success': True,
                'validation_results': validation_results,
                'validation_dir': self.base_validation_dir
            }
            
        except Exception as e:
            self.logger.error(f"多空资金OCR验证失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'validation_results': validation_results
            }
    
    def _test_original_image(self, image: np.ndarray) -> Dict[str, Any]:
        """测试原图OCR识别"""
        start_time = time.time()
        
        try:
            # 保存测试图像
            test_image_path = os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_method1_original.png')
            cv2.imwrite(test_image_path, image)
            
            # OCR识别
            fund_values = self.ocr_engine.recognize_fund_data(image)
            
            execution_time = time.time() - start_time
            
            result = {
                'method': '原图',
                'description': '直接使用截取的原始图像进行OCR识别',
                'image_path': test_image_path,
                'image_size': f"{image.shape[1]}x{image.shape[0]}",
                'fund_values': fund_values,
                'success': len(fund_values) > 0,
                'execution_time': round(execution_time, 3),
                'confidence_score': 1.0 if fund_values else 0.0
            }
            
            self.logger.info(f"原图测试完成: 识别到{len(fund_values)}个数值, 耗时{execution_time:.3f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"原图测试失败: {str(e)}")
            return {
                'method': '原图',
                'description': '直接使用截取的原始图像进行OCR识别',
                'success': False,
                'error': str(e),
                'execution_time': time.time() - start_time
            }
    
    def _test_scaled_image(self, image: np.ndarray, scale_factor: int, method_name: str) -> Dict[str, Any]:
        """测试放大图像OCR识别"""
        start_time = time.time()
        
        try:
            # 图像放大
            height, width = image.shape[:2]
            new_width = width * scale_factor
            new_height = height * scale_factor
            
            # 使用高质量插值方法放大图像
            scaled_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 保存测试图像
            test_image_path = os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_method_{method_name}.png')
            cv2.imwrite(test_image_path, scaled_image)
            
            # OCR识别
            fund_values = self.ocr_engine.recognize_fund_data(scaled_image)
            
            execution_time = time.time() - start_time
            
            result = {
                'method': f'{scale_factor}倍放大',
                'description': f'将原图放大{scale_factor}倍后进行OCR识别，使用LANCZOS4插值',
                'image_path': test_image_path,
                'original_size': f"{width}x{height}",
                'scaled_size': f"{new_width}x{new_height}",
                'scale_factor': scale_factor,
                'fund_values': fund_values,
                'success': len(fund_values) > 0,
                'execution_time': round(execution_time, 3),
                'confidence_score': 1.0 if fund_values else 0.0
            }
            
            self.logger.info(f"{scale_factor}倍放大测试完成: 识别到{len(fund_values)}个数值, 耗时{execution_time:.3f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"{scale_factor}倍放大测试失败: {str(e)}")
            return {
                'method': f'{scale_factor}倍放大',
                'description': f'将原图放大{scale_factor}倍后进行OCR识别',
                'success': False,
                'error': str(e),
                'execution_time': time.time() - start_time
            }
    
    def _test_dpi_enhanced_image(self, image: np.ndarray) -> Dict[str, Any]:
        """测试DPI增强图像OCR识别"""
        start_time = time.time()
        
        try:
            # DPI增强处理
            # 通常DPI300相当于对图像进行约3.125倍的放大 (300/96 = 3.125)
            dpi_scale_factor = 300 / 96  # 从默认96 DPI提升到300 DPI
            
            height, width = image.shape[:2]
            new_width = int(width * dpi_scale_factor)
            new_height = int(height * dpi_scale_factor)
            
            # 使用高质量插值方法放大图像
            dpi_enhanced_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 额外的图像增强处理以提高OCR效果
            # 1. 转换为灰度图
            gray = cv2.cvtColor(dpi_enhanced_image, cv2.COLOR_BGR2GRAY)
            
            # 2. 应用高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # 3. 应用锐化滤波器
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(blurred, -1, kernel)
            
            # 4. 自适应阈值二值化
            binary = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            
            # 转换回BGR格式用于OCR
            enhanced_image = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
            
            # 保存测试图像
            test_image_path = os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_method4_dpi300.png')
            cv2.imwrite(test_image_path, enhanced_image)
            
            # 同时保存中间处理步骤的图像用于调试
            if self.debug_mode:
                cv2.imwrite(os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_method4_dpi300_gray.png'), gray)
                cv2.imwrite(os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_method4_dpi300_blurred.png'), blurred)
                cv2.imwrite(os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_method4_dpi300_sharpened.png'), sharpened)
                cv2.imwrite(os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_method4_dpi300_binary.png'), binary)
            
            # OCR识别
            fund_values = self.ocr_engine.recognize_fund_data(enhanced_image)
            
            execution_time = time.time() - start_time
            
            result = {
                'method': 'DPI300增强',
                'description': 'DPI提升到300并应用图像增强处理（灰度化、去噪、锐化、二值化）',
                'image_path': test_image_path,
                'original_size': f"{width}x{height}",
                'enhanced_size': f"{new_width}x{new_height}",
                'dpi_scale_factor': round(dpi_scale_factor, 3),
                'processing_steps': ['DPI提升', '灰度化', '高斯去噪', '锐化', '自适应二值化'],
                'fund_values': fund_values,
                'success': len(fund_values) > 0,
                'execution_time': round(execution_time, 3),
                'confidence_score': 1.0 if fund_values else 0.0
            }
            
            self.logger.info(f"DPI300增强测试完成: 识别到{len(fund_values)}个数值, 耗时{execution_time:.3f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"DPI300增强测试失败: {str(e)}")
            return {
                'method': 'DPI300增强',
                'description': 'DPI提升到300并应用图像增强处理',
                'success': False,
                'error': str(e),
                'execution_time': time.time() - start_time
            }
    
    def _generate_summary(self, test_methods: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试结果总结"""
        summary = {
            'total_methods': len(test_methods),
            'successful_methods': 0,
            'failed_methods': 0,
            'best_method': None,
            'best_score': 0,
            'recognition_rates': {},
            'performance_comparison': {}
        }
        
        for method_key, result in test_methods.items():
            if result.get('success', False):
                summary['successful_methods'] += 1
                
                # 计算识别率（基于识别到的数值数量）
                fund_count = len(result.get('fund_values', []))
                summary['recognition_rates'][method_key] = fund_count
                
                # 记录性能数据
                summary['performance_comparison'][method_key] = {
                    'execution_time': result.get('execution_time', 0),
                    'fund_count': fund_count,
                    'method_name': result.get('method', method_key)
                }
                
                # 找到最佳方法（基于识别数量和执行时间的综合评分）
                score = fund_count * 10 - result.get('execution_time', 0)  # 识别数量权重更高
                if score > summary['best_score']:
                    summary['best_score'] = score
                    summary['best_method'] = {
                        'key': method_key,
                        'name': result.get('method', method_key),
                        'fund_count': fund_count,
                        'execution_time': result.get('execution_time', 0)
                    }
            else:
                summary['failed_methods'] += 1
        
        return summary
    
    def _generate_recommendations(self, test_methods: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 分析各方法的成功率
        successful_methods = [k for k, v in test_methods.items() if v.get('success', False)]
        failed_methods = [k for k, v in test_methods.items() if not v.get('success', False)]
        
        if not successful_methods:
            recommendations.append("❌ 所有测试方法都失败了，建议检查OCR引擎配置和图像质量")
            recommendations.append("🔧 建议重新选择多空资金区域，确保包含完整的数字内容")
            recommendations.append("🔧 检查屏幕分辨率和缩放设置，确保截取的图像清晰")
        elif len(successful_methods) == 1:
            method_name = test_methods[successful_methods[0]].get('method', '未知方法')
            recommendations.append(f"✅ 只有'{method_name}'方法成功，建议优先使用此方法")
            recommendations.append("⚠️ 其他方法失败，可能需要进一步优化图像处理参数")
        else:
            # 多个方法成功，比较性能
            best_method = None
            best_score = -1
            
            for method_key in successful_methods:
                result = test_methods[method_key]
                fund_count = len(result.get('fund_values', []))
                exec_time = result.get('execution_time', 999)
                score = fund_count * 10 - exec_time  # 识别数量权重更高
                
                if score > best_score:
                    best_score = score
                    best_method = result.get('method', method_key)
            
            if best_method:
                recommendations.append(f"🏆 推荐使用'{best_method}'方法，综合性能最佳")
            
            # 性能分析
            fast_methods = [k for k in successful_methods 
                          if test_methods[k].get('execution_time', 999) < 1.0]
            if fast_methods:
                recommendations.append(f"⚡ 执行速度最快的方法: {', '.join([test_methods[k].get('method', k) for k in fast_methods])}")
            
            # 识别率分析
            high_accuracy_methods = [k for k in successful_methods 
                                   if len(test_methods[k].get('fund_values', [])) >= 3]
            if high_accuracy_methods:
                recommendations.append(f"🎯 识别率最高的方法: {', '.join([test_methods[k].get('method', k) for k in high_accuracy_methods])}")
        
        # 通用建议
        recommendations.append("💡 建议在不同分辨率和屏幕缩放下重复测试以确保稳定性")
        recommendations.append("📊 可以根据实际使用场景选择速度和准确率的平衡点")
        
        return recommendations
    
    def _save_validation_report(self, validation_results: Dict[str, Any]) -> None:
        """保存验证报告"""
        try:
            import json
            
            # 保存JSON格式的详细报告
            json_report_path = os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_validation_report.json')
            with open(json_report_path, 'w', encoding='utf-8') as f:
                json.dump(validation_results, f, ensure_ascii=False, indent=2)
            
            # 生成可读的文本报告
            txt_report_path = os.path.join(self.base_validation_dir, f'{self.current_test_timestamp}_validation_report.txt')
            with open(txt_report_path, 'w', encoding='utf-8') as f:
                f.write("多空资金OCR验证报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"测试时间: {validation_results['timestamp']}\n")
                f.write(f"测试区域: {validation_results['region']}\n\n")
                
                f.write("测试方法结果:\n")
                f.write("-" * 30 + "\n")
                
                for method_key, result in validation_results['test_methods'].items():
                    f.write(f"\n方法: {result.get('method', method_key)}\n")
                    f.write(f"描述: {result.get('description', '无描述')}\n")
                    f.write(f"成功: {'是' if result.get('success', False) else '否'}\n")
                    
                    if result.get('success', False):
                        f.write(f"识别数值: {result.get('fund_values', [])}\n")
                        f.write(f"执行时间: {result.get('execution_time', 0):.3f}秒\n")
                        if 'image_path' in result:
                            f.write(f"测试图像: {result['image_path']}\n")
                    else:
                        f.write(f"错误信息: {result.get('error', '未知错误')}\n")
                    
                    f.write("-" * 20 + "\n")
                
                # 写入总结
                summary = validation_results.get('summary', {})
                f.write(f"\n测试总结:\n")
                f.write(f"总测试方法: {summary.get('total_methods', 0)}\n")
                f.write(f"成功方法: {summary.get('successful_methods', 0)}\n")
                f.write(f"失败方法: {summary.get('failed_methods', 0)}\n")
                
                if summary.get('best_method'):
                    best = summary['best_method']
                    f.write(f"最佳方法: {best.get('name', '未知')} (识别{best.get('fund_count', 0)}个数值，耗时{best.get('execution_time', 0):.3f}秒)\n")
                
                # 写入建议
                f.write(f"\n优化建议:\n")
                for i, rec in enumerate(validation_results.get('recommendations', []), 1):
                    f.write(f"{i}. {rec}\n")
            
            self.logger.info(f"验证报告已保存: {json_report_path}, {txt_report_path}")
            
        except Exception as e:
            self.logger.error(f"保存验证报告失败: {str(e)}")