# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.
Always respond in Chinese-simplified.

## Project Overview

This is a Chinese stock screening tool that automates data extraction from Compass (指南针) financial software using OCR and GUI automation. The application extracts fund flow data for multiple stocks and filters them based on configurable criteria.

## System Requirements and Environment

### Operating System Requirements
- **Windows 10/11**: Required for Compass software integration and pywinauto automation
- **Python 3.8+**: Minimum Python version for compatibility
- **Memory**: Minimum 4GB RAM (8GB+ recommended for OCR operations)
- **Display**: Multi-monitor setup supported for coordinate-based operations

### External Dependencies
- **Compass Software (指南针)**: Must be installed and properly configured
- **Playwright Browser**: Required for web automation features (`playwright install chromium`)
- **CUDA (Optional)**: For GPU-accelerated OCR performance with EasyOCR

### Key Architecture Components

### Core Modules
- **main.py**: Entry point that delegates to gui_main.py 
- **gui_main.py**: Main Tkinter application with modular design using mixin classes
- **config.py**: Central configuration with software paths, OCR settings, and filtering logic
- **compass_automator.py**: Windows automation for Compass software interaction using pywinauto
- **data_processor.py**: Excel file processing and stock filtering logic with pandas
- **compass_data_extractor.py**: Core data extraction logic combining keyboard automation and OCR

### GUI Modules (Mixin Pattern)
- **gui_setup.py**: GUI layout and widget initialization
- **gui_file_operations.py**: Excel file handling and data import/export
- **gui_ocr_operations.py**: OCR region selection and configuration
- **gui_mouse_operations.py**: Mouse position management and operations
- **gui_analysis.py**: Stock analysis workflow and automation
- **gui_display.py**: Results display and data visualization
- **gui_handlers.py**: Event handling and user interactions
- **gui_buy_signal_operations.py**: Buy signal GUI operations (wrapper for unified operations)
- **gui_sell_signal_operations.py**: Sell signal GUI operations (wrapper for unified operations)  
- **gui_signal_operations.py**: Unified GUI operations for both buy and sell signals
- **gui_stock_import_operations.py**: Stock code import and processing operations
- **gui_web_automation_operations.py**: Web automation and data extraction operations

### OCR System (Multi-Engine)
- **simple_ocr_manager.py**: Simplified OCR manager (recommended)
- **ocr_manager_optimized.py**: Complex multi-engine OCR manager
- **ocr_strategy_optimizer.py**: Dynamic OCR strategy selection and optimization
- **paddleocr_compatibility_wrapper.py**: PaddleOCR compatibility wrapper
- **simple_paddleocr_engine.py**: Simplified PaddleOCR engine implementation
- **image_processor.py**: Image preprocessing and enhancement for OCR accuracy

### Specialized Components
- **region_selector.py**: Interactive screen region selection for OCR
- **smart_waiter.py**: Intelligent waiting strategies for GUI automation
- **page_load_detector.py**: Page load detection and refresh completion handling
- **signal_analyzer.py**: Unified signal detection logic for both buy and sell signals
- **unified_signal_monitor.py**: Core unified signal monitoring with configurable behavior
- **base_signal_monitor.py**: Base class for signal monitoring functionality
- **buy_signal_monitor.py**: Buy signal monitoring (wrapper for unified monitor)
- **sell_signal_monitor.py**: Sell signal monitoring (wrapper for unified monitor)
- **dingtalk_notifier.py**: DingTalk integration for notifications
- **web_automator.py**: Web automation using Playwright for stock data extraction
- **html_data_parser.py**: HTML parsing and data extraction from web sources
- **credentials_manager.py**: Secure credential management for web automation
- **data_config_wizard.py**: Interactive configuration wizard for data extraction
- **web_element_config_wizard.py**: Web element configuration wizard
- **web_element_selector.py**: Web element selection utilities
- **version_utils.py**: Version management and update utilities
- **update.py**: Application update and migration handler
- **create_deploy_package.py**: Deployment package creation tool
- **install_dependencies.py**: Smart dependency installer with error handling

## Development Commands

### Installation
```bash
pip install -r requirements.txt
# 或者使用智能安装器（推荐）
python install_dependencies.py
# 首次安装需要安装Playwright浏览器
playwright install chromium
```

### Version Management
```bash
# 检查当前版本
python -c "from version_utils import get_current_version; print(get_current_version())"

# 更新版本
python version_utils.py

# 创建版本化部署包
python create_deploy_package.py
```

### Running the Application
```bash
# 方式1：直接运行Python脚本
python main.py

# 方式2：使用Windows批处理脚本（推荐）
start.bat

# 方式3：指定src目录的入口（注意：start.bat 中使用的路径）
python src/main.py
```

**批处理脚本说明**:
- **start.bat**: 智能启动脚本，自动检测Python环境、版本信息、程序完整性检查、错误处理和修复建议
- **deploy.bat**: 一键部署脚本，自动安装依赖、创建桌面快捷方式、配置环境变量、权限检查

**注意**: start.bat 脚本使用 `src\main.py` 路径，但实际项目中 main.py 位于根目录。这是已知的路径不匹配问题。

### Deployment
```bash
# 创建部署包
python create_deploy_package.py
# 或者使用一键部署脚本（推荐）
deploy.bat
```

### Linting and Code Quality
```bash
# 注意：项目使用中文注释和变量名，无标准化lint工具
# 代码质量检查主要通过功能测试进行
python main.py  # 启动主程序验证基本功能
```

### Testing and Debugging
```bash
# 注意：测试文件不在Git版本控制中，但存在于工作目录

# 核心功能测试
python test_stock_import.py             # 股票导入功能测试
python test_complete_functionality.py   # 完整功能测试
python test_stock_import_functionality.py # 股票导入功能完整测试

# OCR系统测试
python test_ocr_debug.py                # OCR调试测试
python test_improved_ocr.py             # OCR改进测试
python test_improved_ocr_v2.py          # OCR改进版本2测试
python test_color_ocr_hybrid.py         # 颜色OCR混合测试

# 信号检测测试
python test_signal_optimization.py      # 信号优化测试
python test_optimization_simple.py      # 简化优化测试
python test_new_signal_features.py      # 新信号特性测试
python test_empty_signal_recognition.py # 空信号识别测试

# 颜色识别测试
python test_color_recognition.py        # 颜色识别测试
python test_color_fix.py                # 颜色修复测试
python test_color_detection.py          # 颜色检测测试

# Web自动化测试
python test_web_automation.py           # 网页自动化测试
python test_html_parser.py              # HTML解析器测试

# 页面加载和性能测试
python test_page_load_detector.py       # 页面加载检测器测试
python test_page_load_integration.py    # 页面加载集成测试
python test_page_load_performance.py    # 页面加载性能测试

# 其他专项测试
python test_code_analysis.py            # 代码分析测试
python test_virtual_scroll_fix.py       # 虚拟滚动修复测试
python test_integration.py               # 集成测试
python test_failed_stocks.py            # 失败股票测试
python test_fund_ocr_validation.py      # 基金OCR验证测试
python test_dingtalk_logging.py         # 钉钉日志测试
python test_excel_logger.py             # Excel日志测试
```

## Configuration Management

### Main Configuration (`config.py`)
- **COMPASS_SOFTWARE**: Compass software paths, window detection settings, and OCR regions
- **APP_CONFIG**: Application behavior, OCR settings, and processing parameters  
- **GUI_CONFIG**: UI layout and appearance settings
- **FILTER_CONFIG**: Stock filtering criteria and validation rules

### OCR Engine Selection
The system supports two OCR engine modes in `APP_CONFIG['ocr_settings']['engine_mode']`:
- **'simple_paddleocr'**: Simplified PaddleOCR engine (recommended, default)
- **'complex'**: Multi-engine system with EasyOCR and PaddleOCR fallback

### OCR Image Scaling Configuration
New in v2025.08.18: OCR image preprocessing modes in `APP_CONFIG['ocr_settings']['image_scale_mode']`:
- **'original'**: Direct use of captured screenshot (default)
- **'2x'**: 2x image scaling for improved small text recognition
- **'4x'**: 4x image scaling for extremely small or blurred text recognition

### Color Recognition Configuration
Multiple JSON configuration files for comprehensive color and signal detection:
- **`color_config.json`**: Base color recognition settings
- **`signal_color_config.json`**: Signal-specific color configuration
- **`final_color_recognition_config.json`**: Optimized color recognition parameters
- **`recommended_color_config.json`**: Recommended color settings based on analysis
- **`software_update_color_config.json`**: Color settings for software updates
- **Various analysis result files**: `*_results.json`, `*_analysis.json` for configuration optimization

### Web Automation Configuration
- **`web_credentials.json`**: Encrypted credentials for web automation (excluded from Git)
- **`web_element_selectors.json`**: Web element selector configurations for different sites (excluded from Git)
- **`web_element_selectors_template.json`**: Template for web element selectors
- **`web_element_selectors_login_only_template.json`**: Login-specific template

### Dynamic Configuration
- OCR region coordinates are automatically saved to `config.py` when selected via GUI
- The `update_ocr_region_config()` function handles real-time configuration updates

## Key Design Patterns

### Mixin Pattern for GUI
The GUI system uses multiple mixin classes to organize functionality:
- Each mixin handles a specific aspect (file ops, OCR, analysis, etc.)
- Main `StockScreenerGUI` class inherits from all mixins
- Enables modular development and better code organization

### OCR Strategy Pattern
The system employs multiple OCR strategies that are dynamically selected based on:
- Image quality assessment
- Historical performance data
- Content type (text vs percentages)

### Manager Pattern
- **OCRManager**: Global singleton managing multiple OCR engines
- **DataExtractor**: Orchestrates keyboard automation and OCR operations
- **SmartWaiter**: Adaptive timing for GUI operations

### Multi-Engine Fallback
OCR operations use a fallback hierarchy:
1. **Simple Mode (default)**: PaddleOCR with optimized preprocessing
2. **Complex Mode**: EasyOCR with GPU acceleration, PaddleOCR as backup
3. Different preprocessing strategies for each engine based on content type

### Signal Monitoring Architecture
The system uses a unified signal monitoring approach with recent major enhancements:
- **BaseSignalMonitor**: Abstract base class defining signal monitoring interface
- **UnifiedSignalMonitor**: Core implementation handling both buy and sell signals
- **BuySignalMonitor/SellSignalMonitor**: Specialized wrappers with signal-specific configuration
- **Enhanced Signal Analyzer**: Integrated orange signal classification logic with OCR technology
- **Orange Signal Classifier**: New two-level classification system for precise signal identification
- Configurable behavior through `APP_CONFIG` settings for each signal type

### Recent Architecture Improvements (v2025.08.20.1400)
- **二级分类器系统**: Distinguishes between 开仓 (opening position) and 持仓 (holding position) signals
- **OCR集成策略**: Combined color recognition with OCR technology for enhanced accuracy
- **图像缩放处理**: Support for original, 2x, and 4x image scaling modes for better OCR performance
- **智能颜色特征分析**: Automatic analysis of different signal type color characteristics

## Signal Detection System

The application includes a comprehensive signal detection system:
- **Real-time monitoring**: Continuous scanning for buy and sell signals in stock data
- **Unified monitoring**: Single codebase handling both buy and sell signal types
- **DingTalk notifications**: Automatic notifications when signals are detected
- **State tracking**: Monitors position changes and status updates
- **GUI integration**: Signal operations are integrated into the main interface
- **Web-based data extraction**: Alternative data source through web automation
- **Configurable thresholds**: Customizable signal detection criteria per signal type

## Important Development Notes

### Windows-Specific Dependencies
- This application is designed for Windows and requires Compass financial software
- Uses pywinauto for Windows GUI automation
- Screen capture functionality depends on Windows APIs
- Playwright web automation supports cross-platform operation

### OCR Requirements
- First-time PaddleOCR initialization downloads ML models
- EasyOCR (complex mode) downloads large ML models (~100MB+) and requires GPU for optimal performance
- GPU acceleration requires CUDA-compatible hardware
- Debug images are automatically saved to assist with OCR troubleshooting
- Simple PaddleOCR mode is recommended for most use cases

### Threading Architecture
- GUI operations run on main thread
- OCR processing and automation occur in background threads
- Message queue system handles thread communication

### Error Handling Strategy
- OCR operations have built-in retry mechanisms with strategy switching
- Comprehensive error logging and user notification systems
- Graceful degradation when OCR engines fail

### Common Debugging Commands
```bash
# OCR debug with image saving
python -c "from config import APP_CONFIG; print(f'Debug mode: {APP_CONFIG[\"debug_mode\"]}')"

# Check Compass software detection
python -c "from compass_automator import CompassAutomator; CompassAutomator().check_compass_running()"

# Verify screen region coordinates
python region_selector.py  # Interactive region selection tool

# Test OCR engines independently
python simple_ocr_manager.py  # Test PaddleOCR functionality

# Color recognition debugging
python analyze_signal_colors.py  # Analyze signal color characteristics
python simple_color_analyzer.py  # Simplified color analysis tool

# Signal detection debugging
python enhanced_signal_analyzer.py  # Test enhanced signal recognition
python fast_signal_detector.py     # Test optimized signal detection

# Configuration debugging
python data_config_wizard.py       # Interactive configuration setup
python web_element_config_wizard.py # Web element configuration
```

### Debugging Workflows

#### OCR Recognition Issues
1. **Image Quality Check**: Debug images are saved to `ocr_debug_images/` directory
2. **Region Verification**: Use `region_selector.py` to verify OCR region coordinates
3. **Scaling Mode Testing**: Try different image scaling modes (original, 2x, 4x)
4. **Engine Comparison**: Switch between simple and complex OCR modes

#### Signal Detection Issues
1. **Color Analysis**: Use color analysis tools to understand signal characteristics
2. **Classification Testing**: Test the orange signal classifier with different samples
3. **Configuration Optimization**: Use analysis result files to optimize detection parameters

#### Web Automation Issues
1. **Element Selector Validation**: Use web element configuration wizard
2. **Credential Verification**: Check `web_credentials.json` encryption status
3. **Network Connectivity**: Test web automation with different network conditions

## Deployment and Distribution

### Automated Deployment
- `create_deploy_package.py`: Creates portable deployment packages
- `deploy.bat`: Windows batch script for one-click deployment
- Handles dependency installation and configuration setup

### Version Management
- `version.json` and `version.txt`: Track application versions
- `version_utils.py`: Handles application updates and version comparisons
- `update.py`: Handles application updates and migrations
- Version format: vYYYY.MM.DD.HHMM for deployment packages
- **Current version**: v2025.08.24.2140 (Latest: 小草软件适配更新版 - 修复小草软件更新后指标列索引变化导致的数据抓取问题)
- Current development branch: `ocrokv1`
- Deployment packages include version-specific directories and configuration backups

## File Exclusions and Data Management

### Git Repository Structure
The project uses a comprehensive .gitignore that excludes:
- All test files (`test_*.py`) - these are present in working directory but not tracked
- OCR debug images and temporary screenshots
- User data files (Excel, logs, configurations)
- Development tools and IDE configurations
- Web credentials and selector configuration files (`web_credentials.json`, `web_element_selectors.json`)

This keeps the repository focused on core business logic while enabling local development and debugging.

### Important Configuration Files
- **`web_credentials.json`**: Stores encrypted web automation credentials (excluded from Git)
- **`web_element_selectors.json`**: Contains web element selector configurations for different sites (excluded from Git)
- **`web_element_selectors_template.json`**: Template for web element selectors
- **`config.py`**: Main configuration file that gets updated dynamically during runtime
- **Color configuration files**: Various JSON files for color recognition and signal detection (`color_config.json`, `signal_color_config.json`, etc.)
- **Analysis result files**: JSON files containing optimization and analysis results (excluded from Git but present in working directory)

## Code Organization Guidelines

### File Size Management
如果文件超过500行，则按模块或者功能拆分成小文件。当进行代码重构时，应该：
- 将相关功能分组到独立的模块中
- 保持单一职责原则，每个文件专注于特定功能
- 使用清晰的模块命名约定
- 通过导入语句维护模块间的依赖关系

### Mixin Organization
GUI模块使用mixin模式组织，每个mixin文件应该：
- 专注于单一功能领域（如文件操作、OCR操作等）
- 保持方法的独立性，避免过度耦合
- 使用清晰的命名约定（如 `GUIFileOperationsMixin`）
- 在主类中按逻辑顺序继承mixin类

## 项目状态和注意事项

### 当前开发状态
- 项目处于活跃开发状态，当前分支：`ocrokv1`
- **当前版本**: v2025.08.24.2140
- 主要功能已实现，包括OCR数据提取、信号监控、Web自动化
- 推荐使用简化的PaddleOCR引擎以获得最佳性能和稳定性
- Web自动化功能需要初始配置来设置目标网站的元素选择器
- 颜色识别系统已优化，支持多种颜色配置和信号检测
- 具备完整的错误处理和自动恢复机制

### 最新重大功能 (v2025.08.24.2140)
- 🔧 **修复指标列索引配置**: 适配小草软件更新，将所有指标列索引+1（连板接力10→11，竞王11→12，红盘起爆12→13，绿盘低吸13→14）
- ⚡ **修复"获取指标代码"功能**: 解决小草竞王只返回61只股票和红盘起爆多滚页面的问题
- 🎯 **统一配置管理**: 同时更新config.py和web_automator.py中的列索引配置，确保两个功能都使用正确的列位置
- ✅ **保持向后兼容**: 通过配置文件修改实现适配，无需改动核心业务逻辑
- 🚀 **提升数据准确性**: 修复后"开始抓取数据"和"获取指标代码"功能都能正确识别指标值和执行滚动停止条件

### 颜色识别优化成果
项目进行了大量的颜色识别分析和优化工作：
- **多层次配置文件**: 包含基础、优化、推荐等多种颜色配置方案
- **分析结果文件**: 完整的颜色分析报告和优化建议数据
- **信号特征分析**: 详细的信号颜色特征数据和分类结果
- **二级分类系统**: 开仓/持仓信号的精确识别机制

### 启动脚本特性
- **智能环境检测**: 自动检测Python版本和环境完整性
- **错误处理**: 提供详细的错误信息和修复建议
- **自动修复**: 支持依赖包重新安装和配置修复
- **用户友好**: 彩色界面和中文提示，提供清晰的操作指导

### 测试文件说明
项目包含多个测试文件（`test_*.py`），但这些文件：
- 不在Git版本控制中追踪（通过.gitignore排除）
- 主要用于开发过程中的功能验证
- 包含了各个模块的独立测试功能
- 可以用于调试和验证特定功能组件
- 涵盖OCR、信号检测、颜色识别、Web自动化等各个子系统
- 每个测试文件都是自包含的，可以独立运行进行特定功能测试

#### 测试文件生态系统
项目包含60+个测试文件，覆盖以下主要领域：
- **OCR测试**: `test_ocr_debug.py`, `test_improved_ocr*.py`, `test_color_ocr_hybrid.py`
- **信号检测**: `test_signal_optimization.py`, `test_new_signal_features.py`, `test_enhanced_signal_recognition.py`
- **颜色识别**: `test_color_recognition.py`, `test_color_fix.py`, `test_enhanced_orange_recognition.py`
- **配置同步**: `test_enhanced_config_sync.py`, `test_synchronization_fix.py`
- **基金验证**: `test_fund_ocr_validation.py`, `test_fixed_color_recognition.py`
- **系统集成**: `test_integration.py`, `test_complete_functionality.py`
- **性能优化**: `test_optimization_simple.py`, `test_virtual_scroll_fix.py`

### 故障排除和常见问题

#### OCR相关问题
- **模型下载**: 首次运行PaddleOCR会自动下载模型文件到 `models/` 目录
- **图像缩放**: 如果识别效果不佳，尝试调整OCR图像缩放模式
- **区域坐标**: 使用 `region_selector.py` 重新校准OCR区域坐标

#### 信号识别问题
- **橙色信号分类**: 使用二级分类器区分开仓和持仓信号
- **颜色配置**: 参考多个颜色配置文件进行优化调整
- **OCR集成**: 结合OCR技术提升复杂场景识别准确性

#### 路径不匹配问题
- **start.bat路径**: 脚本使用 `src\main.py` 但实际文件在根目录 `main.py`
- **解决方案**: 直接运行 `python main.py` 或修改批处理脚本路径

## 重要开发原则

### 文件创建策略
- **优先编辑现有文件**：始终优先修改现有文件而不是创建新文件
- **最小化文件创建**：只在绝对必要时才创建新文件
- **禁止主动创建文档**：不要主动创建README或Markdown文档文件
- **禁止未要求的测试**：没有明确要求时，不要自己写测试脚本测试修改点

### 代码修改规范
- 遵循现有的中文注释风格和变量命名约定
- 保持与现有代码架构的一致性
- 优先使用项目已有的依赖包和工具
- 修改配置时要考虑向后兼容性