# 股票筛选器版本信息
版本号: v2025.08.24.2140
构建时间: 2025-08-24 21:40:00
构建时间戳: 1756071600
描述: 小草软件适配更新版 - 修复小草软件更新后指标列索引变化导致的数据抓取问题

# 更新内容:
- 🔧 **修复指标列索引配置**：适配小草软件更新，将所有指标列索引+1（连板接力10→11，竞王11→12，红盘起爆12→13，绿盘低吸13→14）
- ⚡ **修复"获取指标代码"功能**：解决小草竟王只返回61只股票和红盘起爆多滚页面的问题
- 🎯 **统一配置管理**：同时更新config.py和web_automator.py中的列索引配置，确保两个功能都使用正确的列位置
- ✅ **保持向后兼容**：通过配置文件修改实现适配，无需改动核心业务逻辑
- 🚀 **提升数据准确性**：修复后"开始抓取数据"和"获取指标代码"功能都能正确识别指标值和执行滚动停止条件

# 版本历史:
## v2025.01.01.0000 (2025-01-01)
- 初始便携式部署版本
- 创建自动化部署和更新机制
- 优化OCR识别准确率
- 完善错误处理和用户体验

# 使用说明:
此文件记录当前部署包的版本信息，包括版本号、构建时间和更新内容。
版本号格式为: vYYYY.MM.DD.HHMM
构建时间戳用于程序内部版本比较。

# 更新检查:
程序启动时会检查此文件以确定当前版本
更新程序会比较版本信息以决定是否需要更新